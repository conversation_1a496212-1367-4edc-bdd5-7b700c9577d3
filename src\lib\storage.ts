import { promises as fs } from 'fs';
import path from 'path';
import { Activity } from '@/types';

const DATA_DIR = path.join(process.cwd(), 'data');
const ACTIVITIES_FILE = path.join(DATA_DIR, 'activities.json');
const UPLOADS_DIR = path.join(DATA_DIR, 'uploads');

// Ensure data directory exists
export async function ensureDataDir() {
  try {
    await fs.access(DATA_DIR);
  } catch {
    await fs.mkdir(DATA_DIR, { recursive: true });
  }
  
  try {
    await fs.access(UPLOADS_DIR);
  } catch {
    await fs.mkdir(UPLOADS_DIR, { recursive: true });
  }
}

// Read activities from JSON file
export async function readActivities(): Promise<Activity[]> {
  try {
    await ensureDataDir();
    const data = await fs.readFile(ACTIVITIES_FILE, 'utf-8');
    const parsed = JSON.parse(data);
    return parsed.activities || [];
  } catch (error) {
    // If file doesn't exist or is invalid, return empty array
    return [];
  }
}

// Write activities to JSON file
export async function writeActivities(activities: Activity[]): Promise<void> {
  await ensureDataDir();
  const data = {
    activities,
    lastUpdated: new Date().toISOString(),
  };
  await fs.writeFile(ACTIVITIES_FILE, JSON.stringify(data, null, 2));
}

// Add a new activity
export async function addActivity(activity: Omit<Activity, 'id' | 'createdAt' | 'updatedAt'>): Promise<Activity> {
  const activities = await readActivities();
  const newActivity: Activity = {
    ...activity,
    id: generateId(),
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };
  
  activities.push(newActivity);
  await writeActivities(activities);
  return newActivity;
}

// Update an existing activity
export async function updateActivity(id: string, updates: Partial<Activity>): Promise<Activity | null> {
  const activities = await readActivities();
  const index = activities.findIndex(a => a.id === id);
  
  if (index === -1) return null;
  
  activities[index] = {
    ...activities[index],
    ...updates,
    updatedAt: new Date().toISOString(),
  };
  
  await writeActivities(activities);
  return activities[index];
}

// Delete an activity
export async function deleteActivity(id: string): Promise<boolean> {
  const activities = await readActivities();
  const filteredActivities = activities.filter(a => a.id !== id);
  
  if (filteredActivities.length === activities.length) {
    return false; // Activity not found
  }
  
  await writeActivities(filteredActivities);
  return true;
}

// Simple ID generator
function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// Save uploaded file
export async function saveUploadedFile(file: File): Promise<string> {
  await ensureDataDir();
  
  const fileName = `${Date.now()}-${file.name}`;
  const filePath = path.join(UPLOADS_DIR, fileName);
  
  const buffer = Buffer.from(await file.arrayBuffer());
  await fs.writeFile(filePath, buffer);
  
  return `uploads/${fileName}`;
}
