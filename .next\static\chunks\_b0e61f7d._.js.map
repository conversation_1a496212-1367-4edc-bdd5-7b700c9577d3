{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/activitiesdates/src/components/IdeaCard.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Idea } from '@/types';\nimport { \n  DollarSign, \n  Edit, \n  Trash2, \n  MessageCircle,\n  CheckCircle,\n  XCircle,\n  Clock,\n  Calendar,\n  ArrowRight\n} from 'lucide-react';\nimport { useRouter } from 'next/navigation';\n\ninterface IdeaCardProps {\n  idea: Idea;\n  onUpdate: (idea: Idea) => void;\n  onDelete: (id: string) => void;\n}\n\nexport default function IdeaCard({ idea, onUpdate, onDelete }: IdeaCardProps) {\n  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);\n  const [showProposalModal, setShowProposalModal] = useState(false);\n  const [showResponseModal, setShowResponseModal] = useState(false);\n  const [responseNotes, setResponseNotes] = useState(idea.responseNotes || '');\n  const [isClient, setIsClient] = useState(false);\n  const router = useRouter();\n\n  useEffect(() => {\n    setIsClient(true);\n  }, []);\n\n  const handlePropose = async () => {\n    try {\n      const updatedIdea = { ...idea, proposed: true };\n\n      const response = await fetch('/api/ideas', {\n        method: 'PUT',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(updatedIdea),\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        onUpdate(data.idea);\n        setShowProposalModal(false);\n      }\n    } catch (error) {\n      console.error('Error proposing idea:', error);\n    }\n  };\n\n  const handleResponse = async (accepted: boolean) => {\n    try {\n      const updatedIdea = { \n        ...idea, \n        accepted, \n        responseNotes: responseNotes.trim() || undefined \n      };\n\n      const response = await fetch('/api/ideas', {\n        method: 'PUT',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(updatedIdea),\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        onUpdate(data.idea);\n        setShowResponseModal(false);\n        setResponseNotes('');\n      }\n    } catch (error) {\n      console.error('Error updating response:', error);\n    }\n  };\n\n  const handleConvertToActivity = async () => {\n    try {\n      const response = await fetch('/api/ideas/convert', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ \n          ideaId: idea.id,\n          price: idea.estimatedPrice || 0,\n        }),\n      });\n\n      if (response.ok) {\n        router.push('/activities');\n      }\n    } catch (error) {\n      console.error('Error converting to activity:', error);\n    }\n  };\n\n  const handleDelete = async () => {\n    try {\n      const response = await fetch(`/api/ideas?id=${idea.id}`, {\n        method: 'DELETE',\n      });\n\n      if (response.ok) {\n        onDelete(idea.id);\n      }\n    } catch (error) {\n      console.error('Error deleting idea:', error);\n    }\n  };\n\n  const getCategoryColor = (category: string) => {\n    const colors = {\n      dining: 'from-orange-400 to-red-500',\n      entertainment: 'from-purple-400 to-pink-500',\n      outdoor: 'from-green-400 to-blue-500',\n      cultural: 'from-indigo-400 to-purple-500',\n      romantic: 'from-pink-400 to-rose-500',\n      adventure: 'from-yellow-400 to-orange-500',\n      relaxation: 'from-blue-400 to-indigo-500',\n      travel: 'from-teal-400 to-cyan-500',\n      sports: 'from-red-400 to-pink-500',\n      other: 'from-gray-400 to-gray-500',\n    };\n    return colors[category as keyof typeof colors] || colors.other;\n  };\n\n  const getStatusInfo = () => {\n    if (!idea.proposed) {\n      return { color: 'bg-blue-100 text-blue-700', text: 'Not Proposed', icon: Clock };\n    }\n    if (idea.accepted === undefined) {\n      return { color: 'bg-yellow-100 text-yellow-700', text: 'Waiting Response', icon: MessageCircle };\n    }\n    if (idea.accepted) {\n      return { color: 'bg-green-100 text-green-700', text: 'Accepted', icon: CheckCircle };\n    }\n    return { color: 'bg-red-100 text-red-700', text: 'Rejected', icon: XCircle };\n  };\n\n  const statusInfo = getStatusInfo();\n  const StatusIcon = statusInfo.icon;\n\n  return (\n    <div className=\"bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-white/20\">\n      {/* Header with category badge */}\n      <div className={`h-2 bg-gradient-to-r ${getCategoryColor(idea.category)}`} />\n      \n      <div className=\"p-6\">\n        {/* Title and Status */}\n        <div className=\"flex items-start justify-between mb-4\">\n          <div className=\"flex-1\">\n            <h3 className=\"text-xl font-bold text-gray-800 mb-2\">\n              {idea.title}\n            </h3>\n            <p className=\"text-gray-600 text-sm leading-relaxed\">\n              {idea.description}\n            </p>\n          </div>\n          \n          <div className={`ml-4 px-3 py-1 rounded-full text-xs font-medium flex items-center space-x-1 ${statusInfo.color}`}>\n            <StatusIcon className=\"h-3 w-3\" />\n            <span>{statusInfo.text}</span>\n          </div>\n        </div>\n\n        {/* Details */}\n        <div className=\"space-y-3 mb-4\">\n          {/* Price */}\n          {idea.estimatedPrice !== undefined && (\n            <div className=\"flex items-center space-x-2 text-gray-600\">\n              <DollarSign className=\"h-4 w-4\" />\n              <span className=\"font-medium\">~${idea.estimatedPrice.toFixed(2)}</span>\n            </div>\n          )}\n        </div>\n\n        {/* Tags */}\n        {idea.tags.length > 0 && (\n          <div className=\"flex flex-wrap gap-2 mb-4\">\n            {idea.tags.map((tag, index) => (\n              <span\n                key={index}\n                className=\"px-2 py-1 bg-yellow-100 text-yellow-700 text-xs rounded-full\"\n              >\n                {tag}\n              </span>\n            ))}\n          </div>\n        )}\n\n        {/* Response Notes */}\n        {idea.responseNotes && (\n          <div className=\"mb-4 p-3 bg-gray-50 rounded-lg\">\n            <p className=\"text-sm text-gray-600 italic\">\"{idea.responseNotes}\"</p>\n          </div>\n        )}\n\n        {/* Actions */}\n        <div className=\"flex items-center justify-between pt-4 border-t border-gray-100\">\n          <div className=\"flex items-center space-x-2\">\n            {!idea.proposed && (\n              <button\n                onClick={() => setShowProposalModal(true)}\n                className=\"px-3 py-1 bg-blue-100 text-blue-700 text-sm rounded-lg hover:bg-blue-200 transition-colors\"\n              >\n                Propose\n              </button>\n            )}\n            \n            {idea.proposed && idea.accepted === undefined && (\n              <button\n                onClick={() => setShowResponseModal(true)}\n                className=\"px-3 py-1 bg-yellow-100 text-yellow-700 text-sm rounded-lg hover:bg-yellow-200 transition-colors\"\n              >\n                Add Response\n              </button>\n            )}\n            \n            {idea.accepted && (\n              <button\n                onClick={handleConvertToActivity}\n                className=\"flex items-center space-x-1 px-3 py-1 bg-green-100 text-green-700 text-sm rounded-lg hover:bg-green-200 transition-colors\"\n              >\n                <Calendar className=\"h-3 w-3\" />\n                <span>Plan It</span>\n                <ArrowRight className=\"h-3 w-3\" />\n              </button>\n            )}\n          </div>\n\n          <div className=\"flex items-center space-x-2\">\n            <button\n              onClick={() => setShowDeleteConfirm(true)}\n              className=\"p-2 rounded-full bg-gray-100 text-gray-400 hover:bg-red-100 hover:text-red-600 transition-all duration-200\"\n              title=\"Delete idea\"\n            >\n              <Trash2 className=\"h-4 w-4\" />\n            </button>\n          </div>\n        </div>\n\n        {/* Category Badge */}\n        <div className=\"flex justify-between items-center mt-4\">\n          <span className={`px-3 py-1 rounded-full text-xs font-medium text-white bg-gradient-to-r ${getCategoryColor(idea.category)}`}>\n            {idea.category.charAt(0).toUpperCase() + idea.category.slice(1)}\n          </span>\n        </div>\n      </div>\n\n      {/* Proposal Modal */}\n      {showProposalModal && (\n        <div className=\"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4\">\n          <div className=\"bg-white rounded-2xl p-6 max-w-sm w-full\">\n            <h3 className=\"text-lg font-bold text-gray-800 mb-2\">Propose This Idea</h3>\n            <p className=\"text-gray-600 mb-6\">\n              Mark this idea as proposed to your girlfriend. You can add her response later.\n            </p>\n            <div className=\"flex space-x-3\">\n              <button\n                onClick={() => setShowProposalModal(false)}\n                className=\"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\"\n              >\n                Cancel\n              </button>\n              <button\n                onClick={handlePropose}\n                className=\"flex-1 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors\"\n              >\n                Propose\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Response Modal */}\n      {showResponseModal && (\n        <div className=\"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4\">\n          <div className=\"bg-white rounded-2xl p-6 max-w-md w-full\">\n            <h3 className=\"text-lg font-bold text-gray-800 mb-4\">Add Her Response</h3>\n            \n            <div className=\"mb-4\">\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Her thoughts/feedback (optional)\n              </label>\n              <textarea\n                value={responseNotes}\n                onChange={(e) => setResponseNotes(e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent resize-none\"\n                rows={3}\n                placeholder=\"What did she say about this idea?\"\n              />\n            </div>\n            \n            <div className=\"flex space-x-3\">\n              <button\n                onClick={() => setShowResponseModal(false)}\n                className=\"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\"\n              >\n                Cancel\n              </button>\n              <button\n                onClick={() => handleResponse(false)}\n                className=\"flex-1 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors\"\n              >\n                Rejected\n              </button>\n              <button\n                onClick={() => handleResponse(true)}\n                className=\"flex-1 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors\"\n              >\n                Accepted\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Delete Confirmation Modal */}\n      {showDeleteConfirm && (\n        <div className=\"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4\">\n          <div className=\"bg-white rounded-2xl p-6 max-w-sm w-full\">\n            <h3 className=\"text-lg font-bold text-gray-800 mb-2\">Delete Idea</h3>\n            <p className=\"text-gray-600 mb-6\">\n              Are you sure you want to delete \"{idea.title}\"? This action cannot be undone.\n            </p>\n            <div className=\"flex space-x-3\">\n              <button\n                onClick={() => setShowDeleteConfirm(false)}\n                className=\"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\"\n              >\n                Cancel\n              </button>\n              <button\n                onClick={() => {\n                  handleDelete();\n                  setShowDeleteConfirm(false);\n                }}\n                className=\"flex-1 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors\"\n              >\n                Delete\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;;;AAfA;;;;AAuBe,SAAS,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAiB;;IAC1E,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,aAAa,IAAI;IACzE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,YAAY;QACd;6BAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,cAAc;gBAAE,GAAG,IAAI;gBAAE,UAAU;YAAK;YAE9C,MAAM,WAAW,MAAM,MAAM,cAAc;gBACzC,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,SAAS,KAAK,IAAI;gBAClB,qBAAqB;YACvB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC;IACF;IAEA,MAAM,iBAAiB,OAAO;QAC5B,IAAI;YACF,MAAM,cAAc;gBAClB,GAAG,IAAI;gBACP;gBACA,eAAe,cAAc,IAAI,MAAM;YACzC;YAEA,MAAM,WAAW,MAAM,MAAM,cAAc;gBACzC,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,SAAS,KAAK,IAAI;gBAClB,qBAAqB;gBACrB,iBAAiB;YACnB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C;IACF;IAEA,MAAM,0BAA0B;QAC9B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ,KAAK,EAAE;oBACf,OAAO,KAAK,cAAc,IAAI;gBAChC;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,OAAO,IAAI,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;QACjD;IACF;IAEA,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,KAAK,EAAE,EAAE,EAAE;gBACvD,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,SAAS,KAAK,EAAE;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAS;YACb,QAAQ;YACR,eAAe;YACf,SAAS;YACT,UAAU;YACV,UAAU;YACV,WAAW;YACX,YAAY;YACZ,QAAQ;YACR,QAAQ;YACR,OAAO;QACT;QACA,OAAO,MAAM,CAAC,SAAgC,IAAI,OAAO,KAAK;IAChE;IAEA,MAAM,gBAAgB;QACpB,IAAI,CAAC,KAAK,QAAQ,EAAE;YAClB,OAAO;gBAAE,OAAO;gBAA6B,MAAM;gBAAgB,MAAM,uMAAA,CAAA,QAAK;YAAC;QACjF;QACA,IAAI,KAAK,QAAQ,KAAK,WAAW;YAC/B,OAAO;gBAAE,OAAO;gBAAiC,MAAM;gBAAoB,MAAM,2NAAA,CAAA,gBAAa;YAAC;QACjG;QACA,IAAI,KAAK,QAAQ,EAAE;YACjB,OAAO;gBAAE,OAAO;gBAA+B,MAAM;gBAAY,MAAM,8NAAA,CAAA,cAAW;YAAC;QACrF;QACA,OAAO;YAAE,OAAO;YAA2B,MAAM;YAAY,MAAM,+MAAA,CAAA,UAAO;QAAC;IAC7E;IAEA,MAAM,aAAa;IACnB,MAAM,aAAa,WAAW,IAAI;IAElC,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAW,CAAC,qBAAqB,EAAE,iBAAiB,KAAK,QAAQ,GAAG;;;;;;0BAEzE,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDACX,KAAK,KAAK;;;;;;kDAEb,6LAAC;wCAAE,WAAU;kDACV,KAAK,WAAW;;;;;;;;;;;;0CAIrB,6LAAC;gCAAI,WAAW,CAAC,4EAA4E,EAAE,WAAW,KAAK,EAAE;;kDAC/G,6LAAC;wCAAW,WAAU;;;;;;kDACtB,6LAAC;kDAAM,WAAW,IAAI;;;;;;;;;;;;;;;;;;kCAK1B,6LAAC;wBAAI,WAAU;kCAEZ,KAAK,cAAc,KAAK,2BACvB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;8CACtB,6LAAC;oCAAK,WAAU;;wCAAc;wCAAG,KAAK,cAAc,CAAC,OAAO,CAAC;;;;;;;;;;;;;;;;;;oBAMlE,KAAK,IAAI,CAAC,MAAM,GAAG,mBAClB,6LAAC;wBAAI,WAAU;kCACZ,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,sBACnB,6LAAC;gCAEC,WAAU;0CAET;+BAHI;;;;;;;;;;oBAUZ,KAAK,aAAa,kBACjB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;;gCAA+B;gCAAE,KAAK,aAAa;gCAAC;;;;;;;;;;;;kCAKrE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;oCACZ,CAAC,KAAK,QAAQ,kBACb,6LAAC;wCACC,SAAS,IAAM,qBAAqB;wCACpC,WAAU;kDACX;;;;;;oCAKF,KAAK,QAAQ,IAAI,KAAK,QAAQ,KAAK,2BAClC,6LAAC;wCACC,SAAS,IAAM,qBAAqB;wCACpC,WAAU;kDACX;;;;;;oCAKF,KAAK,QAAQ,kBACZ,6LAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC;0DAAK;;;;;;0DACN,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;;0CAK5B,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,SAAS,IAAM,qBAAqB;oCACpC,WAAU;oCACV,OAAM;8CAEN,cAAA,6LAAC,6MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAMxB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAW,CAAC,uEAAuE,EAAE,iBAAiB,KAAK,QAAQ,GAAG;sCACzH,KAAK,QAAQ,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,QAAQ,CAAC,KAAK,CAAC;;;;;;;;;;;;;;;;;YAMlE,mCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAuC;;;;;;sCACrD,6LAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAGlC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,qBAAqB;oCACpC,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;YASR,mCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAuC;;;;;;sCAErD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;oCAChD,WAAU;oCACV,MAAM;oCACN,aAAY;;;;;;;;;;;;sCAIhB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,qBAAqB;oCACpC,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;YASR,mCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAuC;;;;;;sCACrD,6LAAC;4BAAE,WAAU;;gCAAqB;gCACE,KAAK,KAAK;gCAAC;;;;;;;sCAE/C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,qBAAqB;oCACpC,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS;wCACP;wCACA,qBAAqB;oCACvB;oCACA,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GAxUwB;;QAMP,qIAAA,CAAA,YAAS;;;KANF", "debugId": null}}, {"offset": {"line": 648, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/activitiesdates/src/app/ideas/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Idea } from '@/types';\nimport IdeaCard from '@/components/IdeaCard';\nimport { Search, Plus, Lightbulb, Filter } from 'lucide-react';\nimport Link from 'next/link';\n\n// Prevent hydration issues\nfunction useIsClient() {\n  const [isClient, setIsClient] = useState(false);\n\n  useEffect(() => {\n    setIsClient(true);\n  }, []);\n\n  return isClient;\n}\n\nexport default function Ideas() {\n  const [ideas, setIdeas] = useState<Idea[]>([]);\n  const [filteredIdeas, setFilteredIdeas] = useState<Idea[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [filterStatus, setFilterStatus] = useState<'all' | 'not-proposed' | 'proposed' | 'accepted' | 'rejected'>('all');\n  const isClient = useIsClient();\n\n  useEffect(() => {\n    if (isClient) {\n      fetchIdeas();\n    }\n  }, [isClient]);\n\n  useEffect(() => {\n    let filtered = ideas;\n\n    // Filter by search term\n    if (searchTerm) {\n      filtered = filtered.filter(idea =>\n        idea.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        idea.description.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        idea.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))\n      );\n    }\n\n    // Filter by status\n    switch (filterStatus) {\n      case 'not-proposed':\n        filtered = filtered.filter(idea => !idea.proposed);\n        break;\n      case 'proposed':\n        filtered = filtered.filter(idea => idea.proposed && idea.accepted === undefined);\n        break;\n      case 'accepted':\n        filtered = filtered.filter(idea => idea.accepted === true);\n        break;\n      case 'rejected':\n        filtered = filtered.filter(idea => idea.accepted === false);\n        break;\n    }\n\n    setFilteredIdeas(filtered);\n  }, [ideas, searchTerm, filterStatus]);\n\n  const fetchIdeas = async () => {\n    try {\n      const response = await fetch('/api/ideas');\n      const data = await response.json();\n      setIdeas(data.ideas || []);\n    } catch (error) {\n      console.error('Error fetching ideas:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleIdeaUpdate = (updatedIdea: Idea) => {\n    setIdeas(prev => \n      prev.map(idea => \n        idea.id === updatedIdea.id ? updatedIdea : idea\n      )\n    );\n  };\n\n  const handleIdeaDelete = (deletedId: string) => {\n    setIdeas(prev => prev.filter(idea => idea.id !== deletedId));\n  };\n\n  const getStatusCounts = () => {\n    return {\n      total: ideas.length,\n      notProposed: ideas.filter(i => !i.proposed).length,\n      proposed: ideas.filter(i => i.proposed && i.accepted === undefined).length,\n      accepted: ideas.filter(i => i.accepted === true).length,\n      rejected: ideas.filter(i => i.accepted === false).length,\n    };\n  };\n\n  const statusCounts = getStatusCounts();\n\n  if (!isClient || loading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-[400px]\">\n        <div className=\"animate-pulse-slow\">\n          <Lightbulb className=\"h-12 w-12 text-yellow-500\" />\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-8\">\n      {/* Header */}\n      <div className=\"text-center space-y-4\">\n        <h1 className=\"text-4xl md:text-6xl font-bold bg-gradient-to-r from-yellow-500 via-orange-500 to-red-500 bg-clip-text text-transparent\">\n          Date Ideas\n        </h1>\n        <p className=\"text-gray-600 text-lg max-w-2xl mx-auto\">\n          Brainstorm and propose perfect date ideas to your girlfriend\n        </p>\n      </div>\n\n      {/* Stats */}\n      <div className=\"grid grid-cols-2 md:grid-cols-5 gap-4\">\n        <div className=\"bg-white/80 backdrop-blur-sm rounded-xl p-4 text-center border border-white/20\">\n          <div className=\"text-2xl font-bold text-gray-800\">{statusCounts.total}</div>\n          <div className=\"text-sm text-gray-600\">Total Ideas</div>\n        </div>\n        <div className=\"bg-blue-50/80 backdrop-blur-sm rounded-xl p-4 text-center border border-blue-200/20\">\n          <div className=\"text-2xl font-bold text-blue-600\">{statusCounts.notProposed}</div>\n          <div className=\"text-sm text-blue-600\">Not Proposed</div>\n        </div>\n        <div className=\"bg-yellow-50/80 backdrop-blur-sm rounded-xl p-4 text-center border border-yellow-200/20\">\n          <div className=\"text-2xl font-bold text-yellow-600\">{statusCounts.proposed}</div>\n          <div className=\"text-sm text-yellow-600\">Waiting Response</div>\n        </div>\n        <div className=\"bg-green-50/80 backdrop-blur-sm rounded-xl p-4 text-center border border-green-200/20\">\n          <div className=\"text-2xl font-bold text-green-600\">{statusCounts.accepted}</div>\n          <div className=\"text-sm text-green-600\">Accepted</div>\n        </div>\n        <div className=\"bg-red-50/80 backdrop-blur-sm rounded-xl p-4 text-center border border-red-200/20\">\n          <div className=\"text-2xl font-bold text-red-600\">{statusCounts.rejected}</div>\n          <div className=\"text-sm text-red-600\">Rejected</div>\n        </div>\n      </div>\n\n      {/* Search and Filters */}\n      <div className=\"flex flex-col lg:flex-row gap-4 items-center justify-between\">\n        <div className=\"flex flex-col sm:flex-row gap-4 flex-1\">\n          <div className=\"relative flex-1 max-w-md\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5\" />\n            <input\n              type=\"text\"\n              placeholder=\"Search ideas...\"\n              value={searchTerm}\n              onChange={(e) => setSearchTerm(e.target.value)}\n              className=\"w-full pl-10 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-200 bg-white/80 backdrop-blur-sm\"\n            />\n          </div>\n          \n          <div className=\"relative\">\n            <Filter className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5\" />\n            <select\n              value={filterStatus}\n              onChange={(e) => setFilterStatus(e.target.value as any)}\n              className=\"pl-10 pr-8 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-200 bg-white/80 backdrop-blur-sm appearance-none\"\n            >\n              <option value=\"all\">All Ideas</option>\n              <option value=\"not-proposed\">Not Proposed</option>\n              <option value=\"proposed\">Waiting Response</option>\n              <option value=\"accepted\">Accepted</option>\n              <option value=\"rejected\">Rejected</option>\n            </select>\n          </div>\n        </div>\n        \n        <Link\n          href=\"/ideas/add\"\n          className=\"flex items-center space-x-2 bg-gradient-to-r from-yellow-500 to-orange-500 text-white px-6 py-3 rounded-xl hover:from-yellow-600 hover:to-orange-600 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1\"\n        >\n          <Plus className=\"h-5 w-5\" />\n          <span className=\"font-medium\">Add Idea</span>\n        </Link>\n      </div>\n\n      {/* Ideas Grid */}\n      {filteredIdeas.length === 0 ? (\n        <div className=\"text-center py-16\">\n          <Lightbulb className=\"h-16 w-16 text-gray-300 mx-auto mb-4\" />\n          <h3 className=\"text-xl font-semibold text-gray-600 mb-2\">\n            {ideas.length === 0 ? 'No ideas yet' : 'No ideas found'}\n          </h3>\n          <p className=\"text-gray-500 mb-6\">\n            {ideas.length === 0 \n              ? 'Start brainstorming your perfect date ideas!'\n              : 'Try adjusting your search or filter'\n            }\n          </p>\n          {ideas.length === 0 && (\n            <Link\n              href=\"/ideas/add\"\n              className=\"inline-flex items-center space-x-2 bg-gradient-to-r from-yellow-500 to-orange-500 text-white px-6 py-3 rounded-xl hover:from-yellow-600 hover:to-orange-600 transition-all duration-200\"\n            >\n              <Plus className=\"h-5 w-5\" />\n              <span>Add Your First Idea</span>\n            </Link>\n          )}\n        </div>\n      ) : (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {filteredIdeas.map((idea, index) => (\n            <div key={idea.id} className=\"animate-fade-in-up\" style={{ animationDelay: `${index * 0.1}s` }}>\n              <IdeaCard\n                idea={idea}\n                onUpdate={handleIdeaUpdate}\n                onDelete={handleIdeaDelete}\n              />\n            </div>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AAAA;AAAA;AAAA;AACA;;;AANA;;;;;AAQA,2BAA2B;AAC3B,SAAS;;IACP,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,YAAY;QACd;gCAAG,EAAE;IAEL,OAAO;AACT;GARS;AAUM,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiE;IAChH,MAAM,WAAW;IAEjB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2BAAE;YACR,IAAI,UAAU;gBACZ;YACF;QACF;0BAAG;QAAC;KAAS;IAEb,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2BAAE;YACR,IAAI,WAAW;YAEf,wBAAwB;YACxB,IAAI,YAAY;gBACd,WAAW,SAAS,MAAM;uCAAC,CAAA,OACzB,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACxD,KAAK,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC9D,KAAK,IAAI,CAAC,IAAI;+CAAC,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;;;YAE3E;YAEA,mBAAmB;YACnB,OAAQ;gBACN,KAAK;oBACH,WAAW,SAAS,MAAM;2CAAC,CAAA,OAAQ,CAAC,KAAK,QAAQ;;oBACjD;gBACF,KAAK;oBACH,WAAW,SAAS,MAAM;2CAAC,CAAA,OAAQ,KAAK,QAAQ,IAAI,KAAK,QAAQ,KAAK;;oBACtE;gBACF,KAAK;oBACH,WAAW,SAAS,MAAM;2CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;;oBACrD;gBACF,KAAK;oBACH,WAAW,SAAS,MAAM;2CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;;oBACrD;YACJ;YAEA,iBAAiB;QACnB;0BAAG;QAAC;QAAO;QAAY;KAAa;IAEpC,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,SAAS,KAAK,KAAK,IAAI,EAAE;QAC3B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,SAAS,CAAA,OACP,KAAK,GAAG,CAAC,CAAA,OACP,KAAK,EAAE,KAAK,YAAY,EAAE,GAAG,cAAc;IAGjD;IAEA,MAAM,mBAAmB,CAAC;QACxB,SAAS,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IACnD;IAEA,MAAM,kBAAkB;QACtB,OAAO;YACL,OAAO,MAAM,MAAM;YACnB,aAAa,MAAM,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,QAAQ,EAAE,MAAM;YAClD,UAAU,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,IAAI,EAAE,QAAQ,KAAK,WAAW,MAAM;YAC1E,UAAU,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,MAAM,MAAM;YACvD,UAAU,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,OAAO,MAAM;QAC1D;IACF;IAEA,MAAM,eAAe;IAErB,IAAI,CAAC,YAAY,SAAS;QACxB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,+MAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;;;;;;;;;;;IAI7B;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA0H;;;;;;kCAGxI,6LAAC;wBAAE,WAAU;kCAA0C;;;;;;;;;;;;0BAMzD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAoC,aAAa,KAAK;;;;;;0CACrE,6LAAC;gCAAI,WAAU;0CAAwB;;;;;;;;;;;;kCAEzC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAoC,aAAa,WAAW;;;;;;0CAC3E,6LAAC;gCAAI,WAAU;0CAAwB;;;;;;;;;;;;kCAEzC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAsC,aAAa,QAAQ;;;;;;0CAC1E,6LAAC;gCAAI,WAAU;0CAA0B;;;;;;;;;;;;kCAE3C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAqC,aAAa,QAAQ;;;;;;0CACzE,6LAAC;gCAAI,WAAU;0CAAyB;;;;;;;;;;;;kCAE1C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAmC,aAAa,QAAQ;;;;;;0CACvE,6LAAC;gCAAI,WAAU;0CAAuB;;;;;;;;;;;;;;;;;;0BAK1C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAU;;;;;;;;;;;;0CAId,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;wCAC/C,WAAU;;0DAEV,6LAAC;gDAAO,OAAM;0DAAM;;;;;;0DACpB,6LAAC;gDAAO,OAAM;0DAAe;;;;;;0DAC7B,6LAAC;gDAAO,OAAM;0DAAW;;;;;;0DACzB,6LAAC;gDAAO,OAAM;0DAAW;;;;;;0DACzB,6LAAC;gDAAO,OAAM;0DAAW;;;;;;;;;;;;;;;;;;;;;;;;kCAK/B,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;;0CAEV,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,6LAAC;gCAAK,WAAU;0CAAc;;;;;;;;;;;;;;;;;;YAKjC,cAAc,MAAM,KAAK,kBACxB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,+MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;kCACrB,6LAAC;wBAAG,WAAU;kCACX,MAAM,MAAM,KAAK,IAAI,iBAAiB;;;;;;kCAEzC,6LAAC;wBAAE,WAAU;kCACV,MAAM,MAAM,KAAK,IACd,iDACA;;;;;;oBAGL,MAAM,MAAM,KAAK,mBAChB,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;;0CAEV,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,6LAAC;0CAAK;;;;;;;;;;;;;;;;;qCAKZ,6LAAC;gBAAI,WAAU;0BACZ,cAAc,GAAG,CAAC,CAAC,MAAM,sBACxB,6LAAC;wBAAkB,WAAU;wBAAqB,OAAO;4BAAE,gBAAgB,GAAG,QAAQ,IAAI,CAAC,CAAC;wBAAC;kCAC3F,cAAA,6LAAC,iIAAA,CAAA,UAAQ;4BACP,MAAM;4BACN,UAAU;4BACV,UAAU;;;;;;uBAJJ,KAAK,EAAE;;;;;;;;;;;;;;;;AAY7B;IA5MwB;;QAML;;;KANK", "debugId": null}}, {"offset": {"line": 1190, "column": 0}, "map": {"version": 3, "file": "dollar-sign.js", "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/activitiesdates/node_modules/lucide-react/src/icons/dollar-sign.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['line', { x1: '12', x2: '12', y1: '2', y2: '22', key: '7eqyqh' }],\n  ['path', { d: 'M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6', key: '1b0p4s' }],\n];\n\n/**\n * @component @name DollarSign\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8bGluZSB4MT0iMTIiIHgyPSIxMiIgeTE9IjIiIHkyPSIyMiIgLz4KICA8cGF0aCBkPSJNMTcgNUg5LjVhMy41IDMuNSAwIDAgMCAwIDdoNWEzLjUgMy41IDAgMCAxIDAgN0g2IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/dollar-sign\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst DollarSign = createLucideIcon('dollar-sign', __iconNode);\n\nexport default DollarSign;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACjE;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqD,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACpF;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAa,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1239, "column": 0}, "map": {"version": 3, "file": "trash-2.js", "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/activitiesdates/node_modules/lucide-react/src/icons/trash-2.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M3 6h18', key: 'd0wm0j' }],\n  ['path', { d: 'M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6', key: '4alrt4' }],\n  ['path', { d: 'M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2', key: 'v07s0e' }],\n  ['line', { x1: '10', x2: '10', y1: '11', y2: '17', key: '1uufr5' }],\n  ['line', { x1: '14', x2: '14', y1: '11', y2: '17', key: 'xtxkd' }],\n];\n\n/**\n * @component @name Trash2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyA2aDE4IiAvPgogIDxwYXRoIGQ9Ik0xOSA2djE0YzAgMS0xIDItMiAySDdjLTEgMC0yLTEtMi0yVjYiIC8+CiAgPHBhdGggZD0iTTggNlY0YzAtMSAxLTIgMi0yaDRjMSAwIDIgMSAyIDJ2MiIgLz4KICA8bGluZSB4MT0iMTAiIHgyPSIxMCIgeTE9IjExIiB5Mj0iMTciIC8+CiAgPGxpbmUgeDE9IjE0IiB4Mj0iMTQiIHkxPSIxMSIgeTI9IjE3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/trash-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Trash2 = createLucideIcon('trash-2', __iconNode);\n\nexport default Trash2;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAyC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACtE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAsC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACnE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAClE;QAAC,MAAA,CAAQ;QAAA,CAAA;YAAE,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,IAAM,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAS,CAAA;KAAA;CACnE;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAW,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1312, "column": 0}, "map": {"version": 3, "file": "message-circle.js", "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/activitiesdates/node_modules/lucide-react/src/icons/message-circle.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M7.9 20A9 9 0 1 0 4 16.1L2 22Z', key: 'vv11sd' }],\n];\n\n/**\n * @component @name MessageCircle\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNy45IDIwQTkgOSAwIDEgMCA0IDE2LjFMMiAyMloiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/message-circle\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MessageCircle = createLucideIcon('message-circle', __iconNode);\n\nexport default MessageCircle;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkC,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACjE;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAgB,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,AAAjB,CAAA,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAkB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1351, "column": 0}, "map": {"version": 3, "file": "circle-check-big.js", "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/activitiesdates/node_modules/lucide-react/src/icons/circle-check-big.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M21.801 10A10 10 0 1 1 17 3.335', key: 'yps3ct' }],\n  ['path', { d: 'm9 11 3 3L22 4', key: '1pflzl' }],\n];\n\n/**\n * @component @name CircleCheckBig\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEuODAxIDEwQTEwIDEwIDAgMSAxIDE3IDMuMzM1IiAvPgogIDxwYXRoIGQ9Im05IDExIDMgM0wyMiA0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/circle-check-big\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CircleCheckBig = createLucideIcon('circle-check-big', __iconNode);\n\nexport default CircleCheckBig;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAmC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAChE;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACjD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,cAAA,CAAiB,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAoB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1397, "column": 0}, "map": {"version": 3, "file": "circle-x.js", "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/activitiesdates/node_modules/lucide-react/src/icons/circle-x.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['path', { d: 'm15 9-6 6', key: '1uzhvr' }],\n  ['path', { d: 'm9 9 6 6', key: 'z0biqf' }],\n];\n\n/**\n * @component @name CircleX\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8cGF0aCBkPSJtMTUgOS02IDYiIC8+CiAgPHBhdGggZD0ibTkgOSA2IDYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/circle-x\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CircleX = createLucideIcon('circle-x', __iconNode);\n\nexport default CircleX;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACzD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAU,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAiB,AAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1452, "column": 0}, "map": {"version": 3, "file": "clock.js", "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/activitiesdates/node_modules/lucide-react/src/icons/clock.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 6v6l4 2', key: 'mmk7yg' }],\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n];\n\n/**\n * @component @name Clock\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgNnY2bDQgMiIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/clock\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Clock = createLucideIcon('clock', __iconNode);\n\nexport default Clock;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC5C;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAK,CAAA,CAAA,CAAA,CAAA,QAAA;QAAU,CAAA;KAAA;CAC3D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1500, "column": 0}, "map": {"version": 3, "file": "arrow-right.js", "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/activitiesdates/node_modules/lucide-react/src/icons/arrow-right.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M5 12h14', key: '1ays0h' }],\n  ['path', { d: 'm12 5 7 7-7 7', key: 'xquz4c' }],\n];\n\n/**\n * @component @name ArrowRight\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSAxMmgxNCIgLz4KICA8cGF0aCBkPSJtMTIgNSA3IDctNyA3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/arrow-right\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowRight = createLucideIcon('arrow-right', __iconNode);\n\nexport default ArrowRight;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAChD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAa,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1546, "column": 0}, "map": {"version": 3, "file": "search.js", "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/activitiesdates/node_modules/lucide-react/src/icons/search.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm21 21-4.34-4.34', key: '14j7rj' }],\n  ['circle', { cx: '11', cy: '11', r: '8', key: '4ej97u' }],\n];\n\n/**\n * @component @name Search\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMjEgMjEtNC4zNC00LjM0IiAvPgogIDxjaXJjbGUgY3g9IjExIiBjeT0iMTEiIHI9IjgiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/search\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Search = createLucideIcon('search', __iconNode);\n\nexport default Search;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAoB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACjD;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAK,CAAA,CAAA,CAAA,CAAA,QAAA;QAAU,CAAA;KAAA;CAC1D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1594, "column": 0}, "map": {"version": 3, "file": "plus.js", "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/activitiesdates/node_modules/lucide-react/src/icons/plus.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M5 12h14', key: '1ays0h' }],\n  ['path', { d: 'M12 5v14', key: 's699le' }],\n];\n\n/**\n * @component @name Plus\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSAxMmgxNCIgLz4KICA8cGF0aCBkPSJNMTIgNXYxNCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/plus\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Plus = createLucideIcon('plus', __iconNode);\n\nexport default Plus;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1640, "column": 0}, "map": {"version": 3, "file": "funnel.js", "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/activitiesdates/node_modules/lucide-react/src/icons/funnel.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z',\n      key: 'sc7q7i',\n    },\n  ],\n];\n\n/**\n * @component @name Funnel\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAgMjBhMSAxIDAgMCAwIC41NTMuODk1bDIgMUExIDEgMCAwIDAgMTQgMjF2LTdhMiAyIDAgMCAxIC41MTctMS4zNDFMMjEuNzQgNC42N0ExIDEgMCAwIDAgMjEgM0gzYTEgMSAwIDAgMC0uNzQyIDEuNjdsNy4yMjUgNy45ODlBMiAyIDAgMCAxIDEwIDE0eiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/funnel\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Funnel = createLucideIcon('funnel', __iconNode);\n\nexport default Funnel;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KACP;CAEJ;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}