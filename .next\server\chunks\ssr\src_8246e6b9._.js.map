{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/activitiesdates/src/components/QuickActivityModal.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useForm } from 'react-hook-form';\nimport { Activity, ActivityFormData, ActivityCategory } from '@/types';\nimport { X, Save, Calendar } from 'lucide-react';\nimport { format } from 'date-fns';\n\ninterface QuickActivityModalProps {\n  selectedDate: Date;\n  onClose: () => void;\n  onActivityCreated: (activity: Activity) => void;\n}\n\nexport default function QuickActivityModal({ selectedDate, onClose, onActivityCreated }: QuickActivityModalProps) {\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [submitError, setSubmitError] = useState('');\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors },\n  } = useForm<ActivityFormData>({\n    defaultValues: {\n      plannedDate: format(selectedDate, 'yyyy-MM-dd'),\n    },\n  });\n\n  const categories: { value: ActivityCategory; label: string }[] = [\n    { value: 'dining', label: 'Dining' },\n    { value: 'entertainment', label: 'Entertainment' },\n    { value: 'outdoor', label: 'Outdoor' },\n    { value: 'cultural', label: 'Cultural' },\n    { value: 'romantic', label: 'Romantic' },\n    { value: 'adventure', label: 'Adventure' },\n    { value: 'relaxation', label: 'Relaxation' },\n    { value: 'travel', label: 'Travel' },\n    { value: 'sports', label: 'Sports' },\n    { value: 'other', label: 'Other' },\n  ];\n\n  const onSubmit = async (data: ActivityFormData) => {\n    setIsSubmitting(true);\n    setSubmitError('');\n\n    try {\n      // Process tags\n      const tags = data.tags\n        ? data.tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0)\n        : [];\n\n      const activityData = {\n        ...data,\n        tags,\n        price: parseFloat(data.price.toString()),\n        duration: data.duration ? parseInt(data.duration.toString()) : undefined,\n      };\n\n      const response = await fetch('/api/activities', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(activityData),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.error || 'Failed to create activity');\n      }\n\n      const result = await response.json();\n      onActivityCreated(result.activity);\n    } catch (error) {\n      console.error('Error creating activity:', error);\n      setSubmitError(error instanceof Error ? error.message : 'Failed to create activity');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <div className=\"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-6 border-b border-gray-200\">\n          <div className=\"flex items-center space-x-3\">\n            <Calendar className=\"h-6 w-6 text-purple-600\" />\n            <div>\n              <h2 className=\"text-xl font-bold text-gray-800\">Add Activity</h2>\n              <p className=\"text-sm text-gray-600\">\n                {format(selectedDate, 'EEEE, MMMM d, yyyy')}\n              </p>\n            </div>\n          </div>\n          <button\n            onClick={onClose}\n            className=\"p-2 rounded-full hover:bg-gray-100 text-gray-400 hover:text-gray-600 transition-colors\"\n          >\n            <X className=\"h-5 w-5\" />\n          </button>\n        </div>\n\n        {/* Form */}\n        <form onSubmit={handleSubmit(onSubmit)} className=\"p-6 space-y-6\">\n          {/* Title */}\n          <div>\n            <label htmlFor=\"title\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Activity Title *\n            </label>\n            <input\n              type=\"text\"\n              id=\"title\"\n              {...register('title', { required: 'Title is required' })}\n              className=\"w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200\"\n              placeholder=\"e.g., Romantic dinner at Italian restaurant\"\n            />\n            {errors.title && (\n              <p className=\"mt-1 text-sm text-red-600\">{errors.title.message}</p>\n            )}\n          </div>\n\n          {/* Description */}\n          <div>\n            <label htmlFor=\"description\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Description *\n            </label>\n            <textarea\n              id=\"description\"\n              rows={3}\n              {...register('description', { required: 'Description is required' })}\n              className=\"w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 resize-none\"\n              placeholder=\"Describe the activity...\"\n            />\n            {errors.description && (\n              <p className=\"mt-1 text-sm text-red-600\">{errors.description.message}</p>\n            )}\n          </div>\n\n          {/* Price and Category Row */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div>\n              <label htmlFor=\"price\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Price *\n              </label>\n              <div className=\"relative\">\n                <span className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500\">$</span>\n                <input\n                  type=\"number\"\n                  id=\"price\"\n                  step=\"0.01\"\n                  min=\"0\"\n                  {...register('price', { \n                    required: 'Price is required',\n                    min: { value: 0, message: 'Price must be positive' }\n                  })}\n                  className=\"w-full pl-8 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200\"\n                  placeholder=\"0.00\"\n                />\n              </div>\n              {errors.price && (\n                <p className=\"mt-1 text-sm text-red-600\">{errors.price.message}</p>\n              )}\n            </div>\n\n            <div>\n              <label htmlFor=\"category\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Category *\n              </label>\n              <select\n                id=\"category\"\n                {...register('category', { required: 'Category is required' })}\n                className=\"w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200\"\n              >\n                <option value=\"\">Select a category</option>\n                {categories.map((category) => (\n                  <option key={category.value} value={category.value}>\n                    {category.label}\n                  </option>\n                ))}\n              </select>\n              {errors.category && (\n                <p className=\"mt-1 text-sm text-red-600\">{errors.category.message}</p>\n              )}\n            </div>\n          </div>\n\n          {/* Location and Duration Row */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div>\n              <label htmlFor=\"location\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Location\n              </label>\n              <input\n                type=\"text\"\n                id=\"location\"\n                {...register('location')}\n                className=\"w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200\"\n                placeholder=\"e.g., Downtown, Central Park\"\n              />\n            </div>\n\n            <div>\n              <label htmlFor=\"duration\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Duration (minutes)\n              </label>\n              <input\n                type=\"number\"\n                id=\"duration\"\n                min=\"1\"\n                {...register('duration')}\n                className=\"w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200\"\n                placeholder=\"e.g., 120\"\n              />\n            </div>\n          </div>\n\n          {/* Tags */}\n          <div>\n            <label htmlFor=\"tags\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Tags\n            </label>\n            <input\n              type=\"text\"\n              id=\"tags\"\n              {...register('tags')}\n              className=\"w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200\"\n              placeholder=\"e.g., romantic, special occasion, weekend\"\n            />\n            <p className=\"mt-1 text-xs text-gray-500\">Separate tags with commas</p>\n          </div>\n\n          {/* Notes */}\n          <div>\n            <label htmlFor=\"notes\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Notes\n            </label>\n            <textarea\n              id=\"notes\"\n              rows={2}\n              {...register('notes')}\n              className=\"w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 resize-none\"\n              placeholder=\"Any special notes or reminders...\"\n            />\n          </div>\n\n          {/* Error Message */}\n          {submitError && (\n            <div className=\"p-4 bg-red-50 border border-red-200 rounded-xl\">\n              <p className=\"text-red-600 text-sm\">{submitError}</p>\n            </div>\n          )}\n\n          {/* Submit Buttons */}\n          <div className=\"flex items-center justify-end space-x-4 pt-6 border-t border-gray-200\">\n            <button\n              type=\"button\"\n              onClick={onClose}\n              className=\"px-6 py-3 border border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 transition-colors\"\n            >\n              Cancel\n            </button>\n            <button\n              type=\"submit\"\n              disabled={isSubmitting}\n              className=\"flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-xl hover:from-purple-600 hover:to-pink-600 transition-all duration-200 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {isSubmitting ? (\n                <>\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent\" />\n                  <span>Creating...</span>\n                </>\n              ) : (\n                <>\n                  <Save className=\"h-4 w-4\" />\n                  <span>Create Activity</span>\n                </>\n              )}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AAAA;AAAA;AACA;AANA;;;;;;AAce,SAAS,mBAAmB,EAAE,YAAY,EAAE,OAAO,EAAE,iBAAiB,EAA2B;IAC9G,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACtB,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAoB;QAC5B,eAAe;YACb,aAAa,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,cAAc;QACpC;IACF;IAEA,MAAM,aAA2D;QAC/D;YAAE,OAAO;YAAU,OAAO;QAAS;QACnC;YAAE,OAAO;YAAiB,OAAO;QAAgB;QACjD;YAAE,OAAO;YAAW,OAAO;QAAU;QACrC;YAAE,OAAO;YAAY,OAAO;QAAW;QACvC;YAAE,OAAO;YAAY,OAAO;QAAW;QACvC;YAAE,OAAO;YAAa,OAAO;QAAY;QACzC;YAAE,OAAO;YAAc,OAAO;QAAa;QAC3C;YAAE,OAAO;YAAU,OAAO;QAAS;QACnC;YAAE,OAAO;YAAU,OAAO;QAAS;QACnC;YAAE,OAAO;YAAS,OAAO;QAAQ;KAClC;IAED,MAAM,WAAW,OAAO;QACtB,gBAAgB;QAChB,eAAe;QAEf,IAAI;YACF,eAAe;YACf,MAAM,OAAO,KAAK,IAAI,GAClB,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI,IAAI,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,GAAG,KACvE,EAAE;YAEN,MAAM,eAAe;gBACnB,GAAG,IAAI;gBACP;gBACA,OAAO,WAAW,KAAK,KAAK,CAAC,QAAQ;gBACrC,UAAU,KAAK,QAAQ,GAAG,SAAS,KAAK,QAAQ,CAAC,QAAQ,MAAM;YACjE;YAEA,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;YACrC;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,kBAAkB,OAAO,QAAQ;QACnC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC1D,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAkC;;;;;;sDAChD,8OAAC;4CAAE,WAAU;sDACV,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,cAAc;;;;;;;;;;;;;;;;;;sCAI5B,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAKjB,8OAAC;oBAAK,UAAU,aAAa;oBAAW,WAAU;;sCAEhD,8OAAC;;8CACC,8OAAC;oCAAM,SAAQ;oCAAQ,WAAU;8CAA+C;;;;;;8CAGhF,8OAAC;oCACC,MAAK;oCACL,IAAG;oCACF,GAAG,SAAS,SAAS;wCAAE,UAAU;oCAAoB,EAAE;oCACxD,WAAU;oCACV,aAAY;;;;;;gCAEb,OAAO,KAAK,kBACX,8OAAC;oCAAE,WAAU;8CAA6B,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;sCAKlE,8OAAC;;8CACC,8OAAC;oCAAM,SAAQ;oCAAc,WAAU;8CAA+C;;;;;;8CAGtF,8OAAC;oCACC,IAAG;oCACH,MAAM;oCACL,GAAG,SAAS,eAAe;wCAAE,UAAU;oCAA0B,EAAE;oCACpE,WAAU;oCACV,aAAY;;;;;;gCAEb,OAAO,WAAW,kBACjB,8OAAC;oCAAE,WAAU;8CAA6B,OAAO,WAAW,CAAC,OAAO;;;;;;;;;;;;sCAKxE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAA+C;;;;;;sDAGhF,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAmE;;;;;;8DACnF,8OAAC;oDACC,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,KAAI;oDACH,GAAG,SAAS,SAAS;wDACpB,UAAU;wDACV,KAAK;4DAAE,OAAO;4DAAG,SAAS;wDAAyB;oDACrD,EAAE;oDACF,WAAU;oDACV,aAAY;;;;;;;;;;;;wCAGf,OAAO,KAAK,kBACX,8OAAC;4CAAE,WAAU;sDAA6B,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;8CAIlE,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAA+C;;;;;;sDAGnF,8OAAC;4CACC,IAAG;4CACF,GAAG,SAAS,YAAY;gDAAE,UAAU;4CAAuB,EAAE;4CAC9D,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAG;;;;;;gDAChB,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC;wDAA4B,OAAO,SAAS,KAAK;kEAC/C,SAAS,KAAK;uDADJ,SAAS,KAAK;;;;;;;;;;;wCAK9B,OAAO,QAAQ,kBACd,8OAAC;4CAAE,WAAU;sDAA6B,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;;;;;;;sCAMvE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAA+C;;;;;;sDAGnF,8OAAC;4CACC,MAAK;4CACL,IAAG;4CACF,GAAG,SAAS,WAAW;4CACxB,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAIhB,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAA+C;;;;;;sDAGnF,8OAAC;4CACC,MAAK;4CACL,IAAG;4CACH,KAAI;4CACH,GAAG,SAAS,WAAW;4CACxB,WAAU;4CACV,aAAY;;;;;;;;;;;;;;;;;;sCAMlB,8OAAC;;8CACC,8OAAC;oCAAM,SAAQ;oCAAO,WAAU;8CAA+C;;;;;;8CAG/E,8OAAC;oCACC,MAAK;oCACL,IAAG;oCACF,GAAG,SAAS,OAAO;oCACpB,WAAU;oCACV,aAAY;;;;;;8CAEd,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAI5C,8OAAC;;8CACC,8OAAC;oCAAM,SAAQ;oCAAQ,WAAU;8CAA+C;;;;;;8CAGhF,8OAAC;oCACC,IAAG;oCACH,MAAM;oCACL,GAAG,SAAS,QAAQ;oCACrB,WAAU;oCACV,aAAY;;;;;;;;;;;;wBAKf,6BACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;sCAKzC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,MAAK;oCACL,UAAU;oCACV,WAAU;8CAET,6BACC;;0DACE,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;0DAAK;;;;;;;qEAGR;;0DACE,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxB", "debugId": null}}, {"offset": {"line": 610, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/activitiesdates/src/app/calendar/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Activity } from '@/types';\nimport { format, startOfMonth, endOfMonth, eachDayOfInterval, isSameDay, isToday } from 'date-fns';\nimport { ChevronLeft, ChevronRight, Calendar as CalendarIcon, Heart, Plus } from 'lucide-react';\nimport QuickActivityModal from '@/components/QuickActivityModal';\n\n// Prevent hydration issues\nfunction useIsClient() {\n  const [isClient, setIsClient] = useState(false);\n\n  useEffect(() => {\n    setIsClient(true);\n  }, []);\n\n  return isClient;\n}\n\nexport default function Calendar() {\n  const [activities, setActivities] = useState<Activity[]>([]);\n  const [currentDate, setCurrentDate] = useState(new Date());\n  const [selectedDate, setSelectedDate] = useState<Date | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [showQuickAdd, setShowQuickAdd] = useState(false);\n  const [quickAddDate, setQuickAddDate] = useState<Date | null>(null);\n  const isClient = useIsClient();\n\n  useEffect(() => {\n    if (isClient) {\n      fetchActivities();\n    }\n  }, [isClient]);\n\n  const fetchActivities = async () => {\n    try {\n      const response = await fetch('/api/activities');\n      const data = await response.json();\n      setActivities(data.activities || []);\n    } catch (error) {\n      console.error('Error fetching activities:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDateClick = (date: Date) => {\n    setSelectedDate(date);\n  };\n\n  const handleDateDoubleClick = (date: Date) => {\n    setQuickAddDate(date);\n    setShowQuickAdd(true);\n  };\n\n  const handleActivityCreated = (newActivity: Activity) => {\n    setActivities(prev => [...prev, newActivity]);\n    setShowQuickAdd(false);\n    setQuickAddDate(null);\n  };\n\n  const monthStart = startOfMonth(currentDate);\n  const monthEnd = endOfMonth(currentDate);\n  const monthDays = eachDayOfInterval({ start: monthStart, end: monthEnd });\n\n  // Get activities for a specific date\n  const getActivitiesForDate = (date: Date) => {\n    return activities.filter(activity => \n      activity.plannedDate && isSameDay(new Date(activity.plannedDate), date)\n    );\n  };\n\n  // Get activities for selected date\n  const selectedDateActivities = selectedDate ? getActivitiesForDate(selectedDate) : [];\n\n  const navigateMonth = (direction: 'prev' | 'next') => {\n    setCurrentDate(prev => {\n      const newDate = new Date(prev);\n      if (direction === 'prev') {\n        newDate.setMonth(prev.getMonth() - 1);\n      } else {\n        newDate.setMonth(prev.getMonth() + 1);\n      }\n      return newDate;\n    });\n  };\n\n  const getCategoryColor = (category: string) => {\n    const colors = {\n      dining: 'bg-orange-500',\n      entertainment: 'bg-purple-500',\n      outdoor: 'bg-green-500',\n      cultural: 'bg-indigo-500',\n      romantic: 'bg-pink-500',\n      adventure: 'bg-yellow-500',\n      relaxation: 'bg-blue-500',\n      travel: 'bg-teal-500',\n      sports: 'bg-red-500',\n      other: 'bg-gray-500',\n    };\n    return colors[category as keyof typeof colors] || colors.other;\n  };\n\n  if (!isClient || loading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-[400px]\">\n        <div className=\"animate-pulse-slow\">\n          <CalendarIcon className=\"h-12 w-12 text-purple-500\" />\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-8\">\n      {/* Header */}\n      <div className=\"text-center space-y-4\">\n        <h1 className=\"text-4xl md:text-6xl font-bold bg-gradient-to-r from-purple-600 via-pink-600 to-orange-500 bg-clip-text text-transparent\">\n          Activity Calendar\n        </h1>\n        <p className=\"text-gray-600 text-lg max-w-2xl mx-auto\">\n          View and plan your activities by date\n        </p>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n        {/* Calendar */}\n        <div className=\"lg:col-span-2\">\n          <div className=\"bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6\">\n            {/* Calendar Header */}\n            <div className=\"flex items-center justify-between mb-6\">\n              <button\n                onClick={() => navigateMonth('prev')}\n                className=\"p-2 rounded-full hover:bg-purple-100 text-purple-600 transition-colors\"\n              >\n                <ChevronLeft className=\"h-5 w-5\" />\n              </button>\n              \n              <h2 className=\"text-xl font-bold text-gray-800\">\n                {format(currentDate, 'MMMM yyyy')}\n              </h2>\n              \n              <button\n                onClick={() => navigateMonth('next')}\n                className=\"p-2 rounded-full hover:bg-purple-100 text-purple-600 transition-colors\"\n              >\n                <ChevronRight className=\"h-5 w-5\" />\n              </button>\n            </div>\n\n            {/* Days of Week */}\n            <div className=\"grid grid-cols-7 gap-1 mb-2\">\n              {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (\n                <div key={day} className=\"p-2 text-center text-sm font-medium text-gray-500\">\n                  {day}\n                </div>\n              ))}\n            </div>\n\n            {/* Calendar Grid */}\n            <div className=\"grid grid-cols-7 gap-1\">\n              {monthDays.map(day => {\n                const dayActivities = getActivitiesForDate(day);\n                const isSelected = selectedDate && isSameDay(day, selectedDate);\n                const isTodayDate = isToday(day);\n\n                return (\n                  <button\n                    key={day.toISOString()}\n                    onClick={() => handleDateClick(day)}\n                    onDoubleClick={() => handleDateDoubleClick(day)}\n                    className={`p-2 min-h-[80px] border border-gray-100 rounded-lg transition-all duration-200 hover:bg-purple-50 relative group ${\n                      isSelected ? 'bg-purple-100 border-purple-300' : ''\n                    } ${isTodayDate ? 'ring-2 ring-purple-300' : ''}`}\n                    title=\"Click to select, double-click to add activity\"\n                  >\n                    <div className=\"text-sm font-medium text-gray-800 mb-1\">\n                      {format(day, 'd')}\n                    </div>\n\n                    {/* Activity indicators */}\n                    <div className=\"space-y-1\">\n                      {dayActivities.slice(0, 3).map((activity, index) => (\n                        <div\n                          key={activity.id}\n                          className={`w-full h-1.5 rounded-full ${getCategoryColor(activity.category)} ${\n                            activity.completed ? 'opacity-50' : ''\n                          }`}\n                          title={activity.title}\n                        />\n                      ))}\n                      {dayActivities.length > 3 && (\n                        <div className=\"text-xs text-gray-500\">\n                          +{dayActivities.length - 3} more\n                        </div>\n                      )}\n                    </div>\n\n                    {/* Quick add button on hover */}\n                    <div className=\"absolute inset-0 bg-purple-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-200 rounded-lg flex items-center justify-center\">\n                      <Plus className=\"h-4 w-4 text-purple-600\" />\n                    </div>\n                  </button>\n                );\n              })}\n            </div>\n          </div>\n        </div>\n\n        {/* Selected Date Activities */}\n        <div className=\"lg:col-span-1\">\n          <div className=\"bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6\">\n            <h3 className=\"text-lg font-bold text-gray-800 mb-4\">\n              {selectedDate ? format(selectedDate, 'MMMM d, yyyy') : 'Select a date'}\n            </h3>\n\n            {selectedDate ? (\n              <>\n                <div className=\"flex items-center justify-between mb-4\">\n                  <span className=\"text-sm text-gray-500\">\n                    {selectedDateActivities.length} activities\n                  </span>\n                  <button\n                    onClick={() => handleDateDoubleClick(selectedDate)}\n                    className=\"flex items-center space-x-1 px-3 py-1 bg-purple-100 text-purple-700 text-sm rounded-lg hover:bg-purple-200 transition-colors\"\n                  >\n                    <Plus className=\"h-3 w-3\" />\n                    <span>Add Activity</span>\n                  </button>\n                </div>\n\n                {selectedDateActivities.length > 0 ? (\n                  <div className=\"space-y-4\">\n                    {selectedDateActivities.map(activity => (\n                      <div\n                        key={activity.id}\n                        className={`p-4 rounded-xl border-l-4 ${\n                          activity.completed ? 'bg-gray-50 opacity-75' : 'bg-white'\n                        }`}\n                        style={{\n                          borderLeftColor: getCategoryColor(activity.category).replace('bg-', '#'),\n                        }}\n                      >\n                        <h4 className={`font-semibold text-gray-800 mb-1 ${\n                          activity.completed ? 'line-through' : ''\n                        }`}>\n                          {activity.title}\n                        </h4>\n                        <p className=\"text-sm text-gray-600 mb-2\">\n                          {activity.description}\n                        </p>\n                        <div className=\"flex items-center justify-between text-xs\">\n                          <span className=\"text-gray-500\">\n                            ${activity.price.toFixed(2)}\n                          </span>\n                          <span className={`px-2 py-1 rounded-full text-white ${getCategoryColor(activity.category)}`}>\n                            {activity.category}\n                          </span>\n                        </div>\n                        {activity.completed && (\n                          <div className=\"mt-2 text-xs text-green-600 font-medium\">\n                            ✓ Completed\n                          </div>\n                        )}\n                      </div>\n                    ))}\n                  </div>\n                ) : (\n                  <div className=\"text-center py-8\">\n                    <CalendarIcon className=\"h-12 w-12 text-gray-300 mx-auto mb-3\" />\n                    <p className=\"text-gray-500 mb-3\">No activities planned for this date</p>\n                    <button\n                      onClick={() => handleDateDoubleClick(selectedDate)}\n                      className=\"inline-flex items-center space-x-2 px-4 py-2 bg-purple-500 text-white text-sm rounded-lg hover:bg-purple-600 transition-colors\"\n                    >\n                      <Plus className=\"h-4 w-4\" />\n                      <span>Add Activity</span>\n                    </button>\n                  </div>\n                )}\n              </>\n            ) : (\n              <div className=\"text-center py-8\">\n                <Heart className=\"h-12 w-12 text-gray-300 mx-auto mb-3\" />\n                <p className=\"text-gray-500 mb-2\">Click on a date to see planned activities</p>\n                <p className=\"text-gray-400 text-sm\">Double-click to add a new activity</p>\n              </div>\n            )}\n          </div>\n\n          {/* Legend */}\n          <div className=\"mt-6 bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6\">\n            <h4 className=\"font-semibold text-gray-800 mb-3\">Category Legend</h4>\n            <div className=\"grid grid-cols-2 gap-2 text-xs\">\n              {[\n                { category: 'dining', label: 'Dining' },\n                { category: 'entertainment', label: 'Entertainment' },\n                { category: 'outdoor', label: 'Outdoor' },\n                { category: 'cultural', label: 'Cultural' },\n                { category: 'romantic', label: 'Romantic' },\n                { category: 'adventure', label: 'Adventure' },\n                { category: 'relaxation', label: 'Relaxation' },\n                { category: 'travel', label: 'Travel' },\n                { category: 'sports', label: 'Sports' },\n                { category: 'other', label: 'Other' },\n              ].map(({ category, label }) => (\n                <div key={category} className=\"flex items-center space-x-2\">\n                  <div className={`w-3 h-3 rounded-full ${getCategoryColor(category)}`} />\n                  <span className=\"text-gray-600\">{label}</span>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Quick Add Activity Modal */}\n      {showQuickAdd && quickAddDate && (\n        <QuickActivityModal\n          selectedDate={quickAddDate}\n          onClose={() => {\n            setShowQuickAdd(false);\n            setQuickAddDate(null);\n          }}\n          onActivityCreated={handleActivityCreated}\n        />\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AANA;;;;;;AAQA,2BAA2B;AAC3B,SAAS;IACP,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY;IACd,GAAG,EAAE;IAEL,OAAO;AACT;AAEe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,WAAW;IAEjB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU;YACZ;QACF;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,cAAc,KAAK,UAAU,IAAI,EAAE;QACrC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,gBAAgB;IAClB;IAEA,MAAM,wBAAwB,CAAC;QAC7B,gBAAgB;QAChB,gBAAgB;IAClB;IAEA,MAAM,wBAAwB,CAAC;QAC7B,cAAc,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QAC5C,gBAAgB;QAChB,gBAAgB;IAClB;IAEA,MAAM,aAAa,CAAA,GAAA,2IAAA,CAAA,eAAY,AAAD,EAAE;IAChC,MAAM,WAAW,CAAA,GAAA,yIAAA,CAAA,aAAU,AAAD,EAAE;IAC5B,MAAM,YAAY,CAAA,GAAA,gJAAA,CAAA,oBAAiB,AAAD,EAAE;QAAE,OAAO;QAAY,KAAK;IAAS;IAEvE,qCAAqC;IACrC,MAAM,uBAAuB,CAAC;QAC5B,OAAO,WAAW,MAAM,CAAC,CAAA,WACvB,SAAS,WAAW,IAAI,CAAA,GAAA,wIAAA,CAAA,YAAS,AAAD,EAAE,IAAI,KAAK,SAAS,WAAW,GAAG;IAEtE;IAEA,mCAAmC;IACnC,MAAM,yBAAyB,eAAe,qBAAqB,gBAAgB,EAAE;IAErF,MAAM,gBAAgB,CAAC;QACrB,eAAe,CAAA;YACb,MAAM,UAAU,IAAI,KAAK;YACzB,IAAI,cAAc,QAAQ;gBACxB,QAAQ,QAAQ,CAAC,KAAK,QAAQ,KAAK;YACrC,OAAO;gBACL,QAAQ,QAAQ,CAAC,KAAK,QAAQ,KAAK;YACrC;YACA,OAAO;QACT;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAS;YACb,QAAQ;YACR,eAAe;YACf,SAAS;YACT,UAAU;YACV,UAAU;YACV,WAAW;YACX,YAAY;YACZ,QAAQ;YACR,QAAQ;YACR,OAAO;QACT;QACA,OAAO,MAAM,CAAC,SAAgC,IAAI,OAAO,KAAK;IAChE;IAEA,IAAI,CAAC,YAAY,SAAS;QACxB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0MAAA,CAAA,WAAY;oBAAC,WAAU;;;;;;;;;;;;;;;;IAIhC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA2H;;;;;;kCAGzI,8OAAC;wBAAE,WAAU;kCAA0C;;;;;;;;;;;;0BAKzD,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAM,cAAc;4CAC7B,WAAU;sDAEV,cAAA,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;sDAGzB,8OAAC;4CAAG,WAAU;sDACX,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,aAAa;;;;;;sDAGvB,8OAAC;4CACC,SAAS,IAAM,cAAc;4CAC7B,WAAU;sDAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAK5B,8OAAC;oCAAI,WAAU;8CACZ;wCAAC;wCAAO;wCAAO;wCAAO;wCAAO;wCAAO;wCAAO;qCAAM,CAAC,GAAG,CAAC,CAAA,oBACrD,8OAAC;4CAAc,WAAU;sDACtB;2CADO;;;;;;;;;;8CAOd,8OAAC;oCAAI,WAAU;8CACZ,UAAU,GAAG,CAAC,CAAA;wCACb,MAAM,gBAAgB,qBAAqB;wCAC3C,MAAM,aAAa,gBAAgB,CAAA,GAAA,wIAAA,CAAA,YAAS,AAAD,EAAE,KAAK;wCAClD,MAAM,cAAc,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD,EAAE;wCAE5B,qBACE,8OAAC;4CAEC,SAAS,IAAM,gBAAgB;4CAC/B,eAAe,IAAM,sBAAsB;4CAC3C,WAAW,CAAC,iHAAiH,EAC3H,aAAa,oCAAoC,GAClD,CAAC,EAAE,cAAc,2BAA2B,IAAI;4CACjD,OAAM;;8DAEN,8OAAC;oDAAI,WAAU;8DACZ,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,KAAK;;;;;;8DAIf,8OAAC;oDAAI,WAAU;;wDACZ,cAAc,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,UAAU,sBACxC,8OAAC;gEAEC,WAAW,CAAC,0BAA0B,EAAE,iBAAiB,SAAS,QAAQ,EAAE,CAAC,EAC3E,SAAS,SAAS,GAAG,eAAe,IACpC;gEACF,OAAO,SAAS,KAAK;+DAJhB,SAAS,EAAE;;;;;wDAOnB,cAAc,MAAM,GAAG,mBACtB,8OAAC;4DAAI,WAAU;;gEAAwB;gEACnC,cAAc,MAAM,GAAG;gEAAE;;;;;;;;;;;;;8DAMjC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;;2CAhCb,IAAI,WAAW;;;;;oCAoC1B;;;;;;;;;;;;;;;;;kCAMN,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDACX,eAAe,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,cAAc,kBAAkB;;;;;;oCAGxD,6BACC;;0DACE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;;4DACb,uBAAuB,MAAM;4DAAC;;;;;;;kEAEjC,8OAAC;wDACC,SAAS,IAAM,sBAAsB;wDACrC,WAAU;;0EAEV,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC;0EAAK;;;;;;;;;;;;;;;;;;4CAIT,uBAAuB,MAAM,GAAG,kBAC/B,8OAAC;gDAAI,WAAU;0DACZ,uBAAuB,GAAG,CAAC,CAAA,yBAC1B,8OAAC;wDAEC,WAAW,CAAC,0BAA0B,EACpC,SAAS,SAAS,GAAG,0BAA0B,YAC/C;wDACF,OAAO;4DACL,iBAAiB,iBAAiB,SAAS,QAAQ,EAAE,OAAO,CAAC,OAAO;wDACtE;;0EAEA,8OAAC;gEAAG,WAAW,CAAC,iCAAiC,EAC/C,SAAS,SAAS,GAAG,iBAAiB,IACtC;0EACC,SAAS,KAAK;;;;;;0EAEjB,8OAAC;gEAAE,WAAU;0EACV,SAAS,WAAW;;;;;;0EAEvB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;;4EAAgB;4EAC5B,SAAS,KAAK,CAAC,OAAO,CAAC;;;;;;;kFAE3B,8OAAC;wEAAK,WAAW,CAAC,kCAAkC,EAAE,iBAAiB,SAAS,QAAQ,GAAG;kFACxF,SAAS,QAAQ;;;;;;;;;;;;4DAGrB,SAAS,SAAS,kBACjB,8OAAC;gEAAI,WAAU;0EAA0C;;;;;;;uDAzBtD,SAAS,EAAE;;;;;;;;;qEAiCtB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0MAAA,CAAA,WAAY;wDAAC,WAAU;;;;;;kEACxB,8OAAC;wDAAE,WAAU;kEAAqB;;;;;;kEAClC,8OAAC;wDACC,SAAS,IAAM,sBAAsB;wDACrC,WAAU;;0EAEV,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC;0EAAK;;;;;;;;;;;;;;;;;;;qEAMd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAClC,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAM3C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,8OAAC;wCAAI,WAAU;kDACZ;4CACC;gDAAE,UAAU;gDAAU,OAAO;4CAAS;4CACtC;gDAAE,UAAU;gDAAiB,OAAO;4CAAgB;4CACpD;gDAAE,UAAU;gDAAW,OAAO;4CAAU;4CACxC;gDAAE,UAAU;gDAAY,OAAO;4CAAW;4CAC1C;gDAAE,UAAU;gDAAY,OAAO;4CAAW;4CAC1C;gDAAE,UAAU;gDAAa,OAAO;4CAAY;4CAC5C;gDAAE,UAAU;gDAAc,OAAO;4CAAa;4CAC9C;gDAAE,UAAU;gDAAU,OAAO;4CAAS;4CACtC;gDAAE,UAAU;gDAAU,OAAO;4CAAS;4CACtC;gDAAE,UAAU;gDAAS,OAAO;4CAAQ;yCACrC,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,iBACxB,8OAAC;gDAAmB,WAAU;;kEAC5B,8OAAC;wDAAI,WAAW,CAAC,qBAAqB,EAAE,iBAAiB,WAAW;;;;;;kEACpE,8OAAC;wDAAK,WAAU;kEAAiB;;;;;;;+CAFzB;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAWnB,gBAAgB,8BACf,8OAAC,wIAAA,CAAA,UAAkB;gBACjB,cAAc;gBACd,SAAS;oBACP,gBAAgB;oBAChB,gBAAgB;gBAClB;gBACA,mBAAmB;;;;;;;;;;;;AAK7B", "debugId": null}}]}