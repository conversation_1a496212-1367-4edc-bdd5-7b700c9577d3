import { NextRequest, NextResponse } from 'next/server';
import { readActivities, addActivity, updateActivity, deleteActivity } from '@/lib/storage';
import { Activity, ActivityCategory } from '@/types';

// GET - Fetch all activities
export async function GET(request: NextRequest) {
  try {
    const activities = await readActivities();
    
    // Apply filters from query params
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category') as ActivityCategory;
    const completed = searchParams.get('completed');
    const search = searchParams.get('search');
    
    let filteredActivities = activities;
    
    if (category) {
      filteredActivities = filteredActivities.filter(a => a.category === category);
    }
    
    if (completed !== null) {
      const isCompleted = completed === 'true';
      filteredActivities = filteredActivities.filter(a => a.completed === isCompleted);
    }
    
    if (search) {
      const searchLower = search.toLowerCase();
      filteredActivities = filteredActivities.filter(a => 
        a.title.toLowerCase().includes(searchLower) ||
        a.description.toLowerCase().includes(searchLower) ||
        a.tags.some(tag => tag.toLowerCase().includes(searchLower))
      );
    }
    
    // Sort by created date (newest first)
    filteredActivities.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
    
    return NextResponse.json({ activities: filteredActivities });
  } catch (error) {
    console.error('Error fetching activities:', error);
    return NextResponse.json({ error: 'Failed to fetch activities' }, { status: 500 });
  }
}

// POST - Create new activity
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate required fields
    if (!body.title || !body.description || body.price === undefined || !body.category) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }
    
    const activityData = {
      title: body.title,
      description: body.description,
      price: parseFloat(body.price),
      category: body.category,
      plannedDate: body.plannedDate || undefined,
      completedDate: undefined,
      images: body.images || [],
      completed: false,
      rating: undefined,
      notes: body.notes || '',
      tags: body.tags || [],
      location: body.location || undefined,
      duration: body.duration ? parseInt(body.duration) : undefined,
    };
    
    const newActivity = await addActivity(activityData);
    return NextResponse.json({ activity: newActivity }, { status: 201 });
  } catch (error) {
    console.error('Error creating activity:', error);
    return NextResponse.json({ error: 'Failed to create activity' }, { status: 500 });
  }
}

// PUT - Update activity
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, ...updates } = body;
    
    if (!id) {
      return NextResponse.json({ error: 'Activity ID is required' }, { status: 400 });
    }
    
    const updatedActivity = await updateActivity(id, updates);
    
    if (!updatedActivity) {
      return NextResponse.json({ error: 'Activity not found' }, { status: 404 });
    }
    
    return NextResponse.json({ activity: updatedActivity });
  } catch (error) {
    console.error('Error updating activity:', error);
    return NextResponse.json({ error: 'Failed to update activity' }, { status: 500 });
  }
}

// DELETE - Delete activity
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');
    
    if (!id) {
      return NextResponse.json({ error: 'Activity ID is required' }, { status: 400 });
    }
    
    const deleted = await deleteActivity(id);
    
    if (!deleted) {
      return NextResponse.json({ error: 'Activity not found' }, { status: 404 });
    }
    
    return NextResponse.json({ message: 'Activity deleted successfully' });
  } catch (error) {
    console.error('Error deleting activity:', error);
    return NextResponse.json({ error: 'Failed to delete activity' }, { status: 500 });
  }
}
