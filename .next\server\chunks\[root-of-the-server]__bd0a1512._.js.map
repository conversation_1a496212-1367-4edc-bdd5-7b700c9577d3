{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/activitiesdates/src/lib/storage.ts"], "sourcesContent": ["import { promises as fs } from 'fs';\nimport path from 'path';\nimport { Activity, Idea } from '@/types';\n\nconst DATA_DIR = path.join(process.cwd(), 'data');\nconst ACTIVITIES_FILE = path.join(DATA_DIR, 'activities.json');\nconst IDEAS_FILE = path.join(DATA_DIR, 'ideas.json');\nconst UPLOADS_DIR = path.join(DATA_DIR, 'uploads');\n\n// Ensure data directory exists\nexport async function ensureDataDir() {\n  try {\n    await fs.access(DATA_DIR);\n  } catch {\n    await fs.mkdir(DATA_DIR, { recursive: true });\n  }\n  \n  try {\n    await fs.access(UPLOADS_DIR);\n  } catch {\n    await fs.mkdir(UPLOADS_DIR, { recursive: true });\n  }\n}\n\n// Read activities from JSON file\nexport async function readActivities(): Promise<Activity[]> {\n  try {\n    await ensureDataDir();\n    const data = await fs.readFile(ACTIVITIES_FILE, 'utf-8');\n    const parsed = JSON.parse(data);\n    return parsed.activities || [];\n  } catch (error) {\n    // If file doesn't exist or is invalid, return empty array\n    return [];\n  }\n}\n\n// Write activities to JSON file\nexport async function writeActivities(activities: Activity[]): Promise<void> {\n  await ensureDataDir();\n  const data = {\n    activities,\n    lastUpdated: new Date().toISOString(),\n  };\n  await fs.writeFile(ACTIVITIES_FILE, JSON.stringify(data, null, 2));\n}\n\n// Add a new activity\nexport async function addActivity(activity: Omit<Activity, 'id' | 'createdAt' | 'updatedAt'>): Promise<Activity> {\n  const activities = await readActivities();\n  const newActivity: Activity = {\n    ...activity,\n    id: generateId(),\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  };\n  \n  activities.push(newActivity);\n  await writeActivities(activities);\n  return newActivity;\n}\n\n// Update an existing activity\nexport async function updateActivity(id: string, updates: Partial<Activity>): Promise<Activity | null> {\n  const activities = await readActivities();\n  const index = activities.findIndex(a => a.id === id);\n  \n  if (index === -1) return null;\n  \n  activities[index] = {\n    ...activities[index],\n    ...updates,\n    updatedAt: new Date().toISOString(),\n  };\n  \n  await writeActivities(activities);\n  return activities[index];\n}\n\n// Delete an activity\nexport async function deleteActivity(id: string): Promise<boolean> {\n  const activities = await readActivities();\n  const filteredActivities = activities.filter(a => a.id !== id);\n  \n  if (filteredActivities.length === activities.length) {\n    return false; // Activity not found\n  }\n  \n  await writeActivities(filteredActivities);\n  return true;\n}\n\n// Simple ID generator\nfunction generateId(): string {\n  return Date.now().toString(36) + Math.random().toString(36).substr(2);\n}\n\n// Save uploaded file\nexport async function saveUploadedFile(file: File): Promise<string> {\n  await ensureDataDir();\n\n  const fileName = `${Date.now()}-${file.name}`;\n  const filePath = path.join(UPLOADS_DIR, fileName);\n\n  const buffer = Buffer.from(await file.arrayBuffer());\n  await fs.writeFile(filePath, buffer);\n\n  return `uploads/${fileName}`;\n}\n\n// IDEAS MANAGEMENT\n\n// Read ideas from JSON file\nexport async function readIdeas(): Promise<Idea[]> {\n  try {\n    await ensureDataDir();\n    const data = await fs.readFile(IDEAS_FILE, 'utf-8');\n    const parsed = JSON.parse(data);\n    return parsed.ideas || [];\n  } catch (error) {\n    // If file doesn't exist or is invalid, return empty array\n    return [];\n  }\n}\n\n// Write ideas to JSON file\nexport async function writeIdeas(ideas: Idea[]): Promise<void> {\n  await ensureDataDir();\n  const data = {\n    ideas,\n    lastUpdated: new Date().toISOString(),\n  };\n  await fs.writeFile(IDEAS_FILE, JSON.stringify(data, null, 2));\n}\n\n// Add a new idea\nexport async function addIdea(idea: Omit<Idea, 'id' | 'createdAt' | 'updatedAt'>): Promise<Idea> {\n  const ideas = await readIdeas();\n  const newIdea: Idea = {\n    ...idea,\n    id: generateId(),\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  };\n\n  ideas.push(newIdea);\n  await writeIdeas(ideas);\n  return newIdea;\n}\n\n// Update an existing idea\nexport async function updateIdea(id: string, updates: Partial<Idea>): Promise<Idea | null> {\n  const ideas = await readIdeas();\n  const index = ideas.findIndex(i => i.id === id);\n\n  if (index === -1) return null;\n\n  ideas[index] = {\n    ...ideas[index],\n    ...updates,\n    updatedAt: new Date().toISOString(),\n  };\n\n  await writeIdeas(ideas);\n  return ideas[index];\n}\n\n// Delete an idea\nexport async function deleteIdea(id: string): Promise<boolean> {\n  const ideas = await readIdeas();\n  const filteredIdeas = ideas.filter(i => i.id !== id);\n\n  if (filteredIdeas.length === ideas.length) {\n    return false; // Idea not found\n  }\n\n  await writeIdeas(filteredIdeas);\n  return true;\n}\n\n// Convert idea to activity\nexport async function convertIdeaToActivity(ideaId: string, activityData: Partial<Activity>): Promise<Activity | null> {\n  const ideas = await readIdeas();\n  const idea = ideas.find(i => i.id === ideaId);\n\n  if (!idea) return null;\n\n  const newActivity: Omit<Activity, 'id' | 'createdAt' | 'updatedAt'> = {\n    title: idea.title,\n    description: idea.description,\n    price: idea.estimatedPrice || 0,\n    category: idea.category,\n    plannedDate: undefined,\n    completedDate: undefined,\n    images: [],\n    completed: false,\n    rating: undefined,\n    notes: idea.notes,\n    tags: idea.tags,\n    location: undefined,\n    duration: undefined,\n    ...activityData,\n  };\n\n  return await addActivity(newActivity);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;;;AAGA,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI;AAC1C,MAAM,kBAAkB,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,UAAU;AAC5C,MAAM,aAAa,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,UAAU;AACvC,MAAM,cAAc,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,UAAU;AAGjC,eAAe;IACpB,IAAI;QACF,MAAM,6FAAA,CAAA,WAAE,CAAC,MAAM,CAAC;IAClB,EAAE,OAAM;QACN,MAAM,6FAAA,CAAA,WAAE,CAAC,KAAK,CAAC,UAAU;YAAE,WAAW;QAAK;IAC7C;IAEA,IAAI;QACF,MAAM,6FAAA,CAAA,WAAE,CAAC,MAAM,CAAC;IAClB,EAAE,OAAM;QACN,MAAM,6FAAA,CAAA,WAAE,CAAC,KAAK,CAAC,aAAa;YAAE,WAAW;QAAK;IAChD;AACF;AAGO,eAAe;IACpB,IAAI;QACF,MAAM;QACN,MAAM,OAAO,MAAM,6FAAA,CAAA,WAAE,CAAC,QAAQ,CAAC,iBAAiB;QAChD,MAAM,SAAS,KAAK,KAAK,CAAC;QAC1B,OAAO,OAAO,UAAU,IAAI,EAAE;IAChC,EAAE,OAAO,OAAO;QACd,0DAA0D;QAC1D,OAAO,EAAE;IACX;AACF;AAGO,eAAe,gBAAgB,UAAsB;IAC1D,MAAM;IACN,MAAM,OAAO;QACX;QACA,aAAa,IAAI,OAAO,WAAW;IACrC;IACA,MAAM,6FAAA,CAAA,WAAE,CAAC,SAAS,CAAC,iBAAiB,KAAK,SAAS,CAAC,MAAM,MAAM;AACjE;AAGO,eAAe,YAAY,QAA0D;IAC1F,MAAM,aAAa,MAAM;IACzB,MAAM,cAAwB;QAC5B,GAAG,QAAQ;QACX,IAAI;QACJ,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;IAEA,WAAW,IAAI,CAAC;IAChB,MAAM,gBAAgB;IACtB,OAAO;AACT;AAGO,eAAe,eAAe,EAAU,EAAE,OAA0B;IACzE,MAAM,aAAa,MAAM;IACzB,MAAM,QAAQ,WAAW,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAEjD,IAAI,UAAU,CAAC,GAAG,OAAO;IAEzB,UAAU,CAAC,MAAM,GAAG;QAClB,GAAG,UAAU,CAAC,MAAM;QACpB,GAAG,OAAO;QACV,WAAW,IAAI,OAAO,WAAW;IACnC;IAEA,MAAM,gBAAgB;IACtB,OAAO,UAAU,CAAC,MAAM;AAC1B;AAGO,eAAe,eAAe,EAAU;IAC7C,MAAM,aAAa,MAAM;IACzB,MAAM,qBAAqB,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAE3D,IAAI,mBAAmB,MAAM,KAAK,WAAW,MAAM,EAAE;QACnD,OAAO,OAAO,qBAAqB;IACrC;IAEA,MAAM,gBAAgB;IACtB,OAAO;AACT;AAEA,sBAAsB;AACtB,SAAS;IACP,OAAO,KAAK,GAAG,GAAG,QAAQ,CAAC,MAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC;AACrE;AAGO,eAAe,iBAAiB,IAAU;IAC/C,MAAM;IAEN,MAAM,WAAW,GAAG,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,IAAI,EAAE;IAC7C,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,aAAa;IAExC,MAAM,SAAS,OAAO,IAAI,CAAC,MAAM,KAAK,WAAW;IACjD,MAAM,6FAAA,CAAA,WAAE,CAAC,SAAS,CAAC,UAAU;IAE7B,OAAO,CAAC,QAAQ,EAAE,UAAU;AAC9B;AAKO,eAAe;IACpB,IAAI;QACF,MAAM;QACN,MAAM,OAAO,MAAM,6FAAA,CAAA,WAAE,CAAC,QAAQ,CAAC,YAAY;QAC3C,MAAM,SAAS,KAAK,KAAK,CAAC;QAC1B,OAAO,OAAO,KAAK,IAAI,EAAE;IAC3B,EAAE,OAAO,OAAO;QACd,0DAA0D;QAC1D,OAAO,EAAE;IACX;AACF;AAGO,eAAe,WAAW,KAAa;IAC5C,MAAM;IACN,MAAM,OAAO;QACX;QACA,aAAa,IAAI,OAAO,WAAW;IACrC;IACA,MAAM,6FAAA,CAAA,WAAE,CAAC,SAAS,CAAC,YAAY,KAAK,SAAS,CAAC,MAAM,MAAM;AAC5D;AAGO,eAAe,QAAQ,IAAkD;IAC9E,MAAM,QAAQ,MAAM;IACpB,MAAM,UAAgB;QACpB,GAAG,IAAI;QACP,IAAI;QACJ,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;IAEA,MAAM,IAAI,CAAC;IACX,MAAM,WAAW;IACjB,OAAO;AACT;AAGO,eAAe,WAAW,EAAU,EAAE,OAAsB;IACjE,MAAM,QAAQ,MAAM;IACpB,MAAM,QAAQ,MAAM,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAE5C,IAAI,UAAU,CAAC,GAAG,OAAO;IAEzB,KAAK,CAAC,MAAM,GAAG;QACb,GAAG,KAAK,CAAC,MAAM;QACf,GAAG,OAAO;QACV,WAAW,IAAI,OAAO,WAAW;IACnC;IAEA,MAAM,WAAW;IACjB,OAAO,KAAK,CAAC,MAAM;AACrB;AAGO,eAAe,WAAW,EAAU;IACzC,MAAM,QAAQ,MAAM;IACpB,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAEjD,IAAI,cAAc,MAAM,KAAK,MAAM,MAAM,EAAE;QACzC,OAAO,OAAO,iBAAiB;IACjC;IAEA,MAAM,WAAW;IACjB,OAAO;AACT;AAGO,eAAe,sBAAsB,MAAc,EAAE,YAA+B;IACzF,MAAM,QAAQ,MAAM;IACpB,MAAM,OAAO,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAEtC,IAAI,CAAC,MAAM,OAAO;IAElB,MAAM,cAAgE;QACpE,OAAO,KAAK,KAAK;QACjB,aAAa,KAAK,WAAW;QAC7B,OAAO,KAAK,cAAc,IAAI;QAC9B,UAAU,KAAK,QAAQ;QACvB,aAAa;QACb,eAAe;QACf,QAAQ,EAAE;QACV,WAAW;QACX,QAAQ;QACR,OAAO,KAAK,KAAK;QACjB,MAAM,KAAK,IAAI;QACf,UAAU;QACV,UAAU;QACV,GAAG,YAAY;IACjB;IAEA,OAAO,MAAM,YAAY;AAC3B", "debugId": null}}, {"offset": {"line": 259, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/activitiesdates/src/app/api/upload/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { saveUploadedFile } from '@/lib/storage';\n\nexport async function POST(request: NextRequest) {\n  try {\n    const formData = await request.formData();\n    const file = formData.get('file') as File;\n\n    if (!file) {\n      return NextResponse.json({ error: 'No file provided' }, { status: 400 });\n    }\n\n    // Check file type\n    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];\n    if (!allowedTypes.includes(file.type)) {\n      return NextResponse.json({ error: 'Invalid file type. Only images are allowed.' }, { status: 400 });\n    }\n\n    // Check file size (max 5MB)\n    const maxSize = 5 * 1024 * 1024; // 5MB\n    if (file.size > maxSize) {\n      return NextResponse.json({ error: 'File too large. Maximum size is 5MB.' }, { status: 400 });\n    }\n\n    const filePath = await saveUploadedFile(file);\n    \n    return NextResponse.json({ filePath }, { status: 201 });\n  } catch (error) {\n    console.error('Error uploading file:', error);\n    return NextResponse.json({ error: 'Failed to upload file' }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,QAAQ,QAAQ;QACvC,MAAM,OAAO,SAAS,GAAG,CAAC;QAE1B,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAmB,GAAG;gBAAE,QAAQ;YAAI;QACxE;QAEA,kBAAkB;QAClB,MAAM,eAAe;YAAC;YAAc;YAAa;YAAa;YAAa;SAAa;QACxF,IAAI,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAAG;YACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAA8C,GAAG;gBAAE,QAAQ;YAAI;QACnG;QAEA,4BAA4B;QAC5B,MAAM,UAAU,IAAI,OAAO,MAAM,MAAM;QACvC,IAAI,KAAK,IAAI,GAAG,SAAS;YACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAuC,GAAG;gBAAE,QAAQ;YAAI;QAC5F;QAEA,MAAM,WAAW,MAAM,CAAA,GAAA,uHAAA,CAAA,mBAAgB,AAAD,EAAE;QAExC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE;QAAS,GAAG;YAAE,QAAQ;QAAI;IACvD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAwB,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF", "debugId": null}}]}