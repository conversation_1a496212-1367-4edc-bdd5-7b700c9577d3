'use client';

import { useState } from 'react';
import { Activity } from '@/types';
import {
  Calendar,
  MapPin,
  DollarSign,
  Clock,
  Star,
  Edit,
  Trash2,
  Check,
  X,
  Heart,
  Image as ImageIcon
} from 'lucide-react';
import { format } from 'date-fns';
import { useRouter } from 'next/navigation';

interface ActivityCardProps {
  activity: Activity;
  onUpdate: (activity: Activity) => void;
  onDelete: (id: string) => void;
}

export default function ActivityCard({ activity, onUpdate, onDelete }: ActivityCardProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  const router = useRouter();

  const handleComplete = async () => {
    try {
      const updatedActivity = {
        ...activity,
        completed: !activity.completed,
        completedDate: !activity.completed ? new Date().toISOString() : undefined,
      };

      const response = await fetch('/api/activities', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updatedActivity),
      });

      if (response.ok) {
        const data = await response.json();
        onUpdate(data.activity);
      }
    } catch (error) {
      console.error('Error updating activity:', error);
    }
  };

  const handleDelete = async () => {
    try {
      const response = await fetch(`/api/activities?id=${activity.id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        onDelete(activity.id);
      }
    } catch (error) {
      console.error('Error deleting activity:', error);
    }
  };

  const handleRating = async (rating: number) => {
    try {
      const updatedActivity = { ...activity, rating };

      const response = await fetch('/api/activities', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updatedActivity),
      });

      if (response.ok) {
        const data = await response.json();
        onUpdate(data.activity);
      }
    } catch (error) {
      console.error('Error updating rating:', error);
    }
  };



  const getCategoryColor = (category: string) => {
    const colors = {
      dining: 'from-orange-400 to-red-500',
      entertainment: 'from-purple-400 to-pink-500',
      outdoor: 'from-green-400 to-blue-500',
      cultural: 'from-indigo-400 to-purple-500',
      romantic: 'from-pink-400 to-rose-500',
      adventure: 'from-yellow-400 to-orange-500',
      relaxation: 'from-blue-400 to-indigo-500',
      travel: 'from-teal-400 to-cyan-500',
      sports: 'from-red-400 to-pink-500',
      other: 'from-gray-400 to-gray-500',
    };
    return colors[category as keyof typeof colors] || colors.other;
  };

  const handleCardClick = (e: React.MouseEvent) => {
    // Don't navigate if clicking on buttons or interactive elements
    const target = e.target as HTMLElement;
    if (target.closest('button') || target.closest('input')) {
      return;
    }
    router.push(`/activities/${activity.id}`);
  };

  return (
    <div className={`bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-white/20 cursor-pointer ${
      activity.completed ? 'ring-2 ring-green-200' : ''
    }`}>
      {/* Header with category badge */}
      <div className={`h-2 bg-gradient-to-r ${getCategoryColor(activity.category)}`} />

      <div className="p-6" onClick={handleCardClick}>
        {/* Title and Actions */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <h3 className={`text-xl font-bold text-gray-800 mb-2 ${activity.completed ? 'line-through text-gray-500' : ''}`}>
              {activity.title}
            </h3>
            <p className="text-gray-600 text-sm leading-relaxed">
              {activity.description}
            </p>
          </div>
          
          <div className="flex items-center space-x-2 ml-4">
            <button
              onClick={handleComplete}
              className={`p-2 rounded-full transition-all duration-200 ${
                activity.completed
                  ? 'bg-green-100 text-green-600 hover:bg-green-200'
                  : 'bg-gray-100 text-gray-400 hover:bg-green-100 hover:text-green-600'
              }`}
              title={activity.completed ? 'Mark as incomplete' : 'Mark as complete'}
            >
              <Check className="h-4 w-4" />
            </button>



            <button
              onClick={() => setShowDeleteConfirm(true)}
              className="p-2 rounded-full bg-gray-100 text-gray-400 hover:bg-red-100 hover:text-red-600 transition-all duration-200"
              title="Delete activity"
            >
              <Trash2 className="h-4 w-4" />
            </button>
          </div>
        </div>

        {/* Details */}
        <div className="space-y-3 mb-4">
          {/* Price */}
          <div className="flex items-center space-x-2 text-gray-600">
            <DollarSign className="h-4 w-4" />
            <span className="font-medium">${activity.price.toFixed(2)}</span>
          </div>

          {/* Date */}
          {activity.plannedDate && (
            <div className="flex items-center space-x-2 text-gray-600">
              <Calendar className="h-4 w-4" />
              <span>{format(new Date(activity.plannedDate), 'MMM dd, yyyy')}</span>
            </div>
          )}

          {/* Location */}
          {activity.location && (
            <div className="flex items-center space-x-2 text-gray-600">
              <MapPin className="h-4 w-4" />
              <span>{activity.location}</span>
            </div>
          )}

          {/* Duration */}
          {activity.duration && (
            <div className="flex items-center space-x-2 text-gray-600">
              <Clock className="h-4 w-4" />
              <span>{activity.duration} minutes</span>
            </div>
          )}
        </div>

        {/* Tags */}
        {activity.tags.length > 0 && (
          <div className="flex flex-wrap gap-2 mb-4">
            {activity.tags.map((tag, index) => (
              <span
                key={index}
                className="px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded-full"
              >
                {tag}
              </span>
            ))}
          </div>
        )}

        {/* Photo count indicator (only show if completed and has photos) */}
        {activity.completed && activity.images.length > 0 && (
          <div className="flex items-center space-x-2 text-gray-600 mb-4">
            <ImageIcon className="h-4 w-4" />
            <span className="text-sm font-medium">{activity.images.length} photo{activity.images.length !== 1 ? 's' : ''}</span>
          </div>
        )}

        {/* Rating (only show if completed) */}
        {activity.completed && (
          <div className="flex items-center space-x-1 mb-4">
            <span className="text-sm text-gray-600 mr-2">Rate this activity:</span>
            {[1, 2, 3, 4, 5].map((star) => (
              <button
                key={star}
                onClick={() => handleRating(star)}
                className="transition-colors duration-200"
              >
                <Star
                  className={`h-5 w-5 ${
                    star <= (activity.rating || 0)
                      ? 'text-yellow-400 fill-current'
                      : 'text-gray-300'
                  }`}
                />
              </button>
            ))}
          </div>
        )}

        {/* Category Badge */}
        <div className="flex justify-between items-center">
          <span className={`px-3 py-1 rounded-full text-xs font-medium text-white bg-gradient-to-r ${getCategoryColor(activity.category)}`}>
            {activity.category.charAt(0).toUpperCase() + activity.category.slice(1)}
          </span>
          
          {activity.completed && (
            <div className="flex items-center space-x-1 text-green-600">
              <Check className="h-4 w-4" />
              <span className="text-xs font-medium">Completed</span>
            </div>
          )}
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl p-6 max-w-sm w-full">
            <h3 className="text-lg font-bold text-gray-800 mb-2">Delete Activity</h3>
            <p className="text-gray-600 mb-6">
              Are you sure you want to delete "{activity.title}"? This action cannot be undone.
            </p>
            <div className="flex space-x-3">
              <button
                onClick={() => setShowDeleteConfirm(false)}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  handleDelete();
                  setShowDeleteConfirm(false);
                }}
                className="flex-1 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
