'use client';

import { useState, useEffect, useRef } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Activity } from '@/types';
import { 
  ArrowLeft, 
  Calendar, 
  MapPin, 
  DollarSign, 
  Clock, 
  Star, 
  Check,
  Trash2,
  Camera,
  X,
  Edit,
  Save,
  Image as ImageIcon
} from 'lucide-react';
import { format } from 'date-fns';
import Image from 'next/image';
import Link from 'next/link';

export default function ActivityDetail() {
  const params = useParams();
  const router = useRouter();
  const [activity, setActivity] = useState<Activity | null>(null);
  const [loading, setLoading] = useState(true);
  const [isUploading, setIsUploading] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState<number | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (params.id) {
      fetchActivity(params.id as string);
    }
  }, [params.id]);

  const fetchActivity = async (id: string) => {
    try {
      const response = await fetch('/api/activities');
      const data = await response.json();
      const foundActivity = data.activities?.find((a: Activity) => a.id === id);
      
      if (foundActivity) {
        setActivity(foundActivity);
      } else {
        router.push('/activities');
      }
    } catch (error) {
      console.error('Error fetching activity:', error);
      router.push('/activities');
    } finally {
      setLoading(false);
    }
  };

  const handleComplete = async () => {
    if (!activity) return;

    try {
      const updatedActivity = {
        ...activity,
        completed: !activity.completed,
        completedDate: !activity.completed ? new Date().toISOString() : undefined,
      };

      const response = await fetch('/api/activities', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updatedActivity),
      });

      if (response.ok) {
        const data = await response.json();
        setActivity(data.activity);
      }
    } catch (error) {
      console.error('Error updating activity:', error);
    }
  };

  const handleRating = async (rating: number) => {
    if (!activity) return;

    try {
      const updatedActivity = { ...activity, rating };

      const response = await fetch('/api/activities', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updatedActivity),
      });

      if (response.ok) {
        const data = await response.json();
        setActivity(data.activity);
      }
    } catch (error) {
      console.error('Error updating rating:', error);
    }
  };

  const handlePhotoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !activity) return;

    setIsUploading(true);
    try {
      const formData = new FormData();
      formData.append('file', file);

      const uploadResponse = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });

      if (!uploadResponse.ok) {
        const errorData = await uploadResponse.json();
        throw new Error(errorData.error || 'Failed to upload photo');
      }

      const { filePath } = await uploadResponse.json();

      // Update activity with new image
      const updatedActivity = {
        ...activity,
        images: [...activity.images, filePath],
      };

      const response = await fetch('/api/activities', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updatedActivity),
      });

      if (response.ok) {
        const data = await response.json();
        setActivity(data.activity);
      }
    } catch (error) {
      console.error('Error uploading photo:', error);
      alert('Failed to upload photo. Please try again.');
    } finally {
      setIsUploading(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleRemovePhoto = async (photoPath: string) => {
    if (!activity) return;

    try {
      const updatedActivity = {
        ...activity,
        images: activity.images.filter(img => img !== photoPath),
      };

      const response = await fetch('/api/activities', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updatedActivity),
      });

      if (response.ok) {
        const data = await response.json();
        setActivity(data.activity);
      }
    } catch (error) {
      console.error('Error removing photo:', error);
    }
  };

  const handleDelete = async () => {
    if (!activity) return;

    try {
      const response = await fetch(`/api/activities?id=${activity.id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        router.push('/activities');
      }
    } catch (error) {
      console.error('Error deleting activity:', error);
    }
  };

  const getCategoryColor = (category: string) => {
    const colors = {
      dining: 'from-orange-400 to-red-500',
      entertainment: 'from-purple-400 to-pink-500',
      outdoor: 'from-green-400 to-blue-500',
      cultural: 'from-indigo-400 to-purple-500',
      romantic: 'from-pink-400 to-rose-500',
      adventure: 'from-yellow-400 to-orange-500',
      relaxation: 'from-blue-400 to-indigo-500',
      travel: 'from-teal-400 to-cyan-500',
      sports: 'from-red-400 to-pink-500',
      other: 'from-gray-400 to-gray-500',
    };
    return colors[category as keyof typeof colors] || colors.other;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-pulse-slow">
          <Calendar className="h-12 w-12 text-purple-500" />
        </div>
      </div>
    );
  }

  if (!activity) {
    return (
      <div className="text-center py-16">
        <h1 className="text-2xl font-bold text-gray-800 mb-4">Activity Not Found</h1>
        <Link
          href="/activities"
          className="inline-flex items-center space-x-2 bg-purple-500 text-white px-6 py-3 rounded-xl hover:bg-purple-600 transition-colors"
        >
          <ArrowLeft className="h-4 w-4" />
          <span>Back to Activities</span>
        </Link>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Link
          href="/activities"
          className="p-2 rounded-full bg-white/80 backdrop-blur-sm border border-white/20 text-gray-600 hover:text-purple-600 transition-colors"
        >
          <ArrowLeft className="h-5 w-5" />
        </Link>
        <div className="flex-1">
          <div className="flex items-center space-x-3 mb-2">
            <h1 className="text-3xl font-bold text-gray-800">{activity.title}</h1>
            {activity.completed && (
              <div className="flex items-center space-x-1 text-green-600">
                <Check className="h-5 w-5" />
                <span className="text-sm font-medium">Completed</span>
              </div>
            )}
          </div>
          <span className={`inline-block px-3 py-1 rounded-full text-xs font-medium text-white bg-gradient-to-r ${getCategoryColor(activity.category)}`}>
            {activity.category.charAt(0).toUpperCase() + activity.category.slice(1)}
          </span>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={handleComplete}
            className={`p-3 rounded-full transition-all duration-200 ${
              activity.completed
                ? 'bg-green-100 text-green-600 hover:bg-green-200'
                : 'bg-gray-100 text-gray-400 hover:bg-green-100 hover:text-green-600'
            }`}
            title={activity.completed ? 'Mark as incomplete' : 'Mark as complete'}
          >
            <Check className="h-5 w-5" />
          </button>
          
          <button
            onClick={() => setShowDeleteConfirm(true)}
            className="p-3 rounded-full bg-gray-100 text-gray-400 hover:bg-red-100 hover:text-red-600 transition-all duration-200"
            title="Delete activity"
          >
            <Trash2 className="h-5 w-5" />
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Activity Details */}
        <div className="lg:col-span-2 space-y-6">
          {/* Description */}
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6">
            <h2 className="text-xl font-bold text-gray-800 mb-4">Description</h2>
            <p className="text-gray-600 leading-relaxed">{activity.description}</p>
          </div>

          {/* Photos Section */}
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-bold text-gray-800">Photos</h2>
              {activity.completed && (
                <button
                  onClick={() => fileInputRef.current?.click()}
                  disabled={isUploading}
                  className="flex items-center space-x-2 bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors disabled:opacity-50"
                >
                  {isUploading ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent" />
                  ) : (
                    <Camera className="h-4 w-4" />
                  )}
                  <span>Add Photo</span>
                </button>
              )}
            </div>

            {activity.images.length > 0 ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                {activity.images.map((imagePath, index) => (
                  <div key={index} className="relative group">
                    <Image
                      src={`/api/data/${imagePath}`}
                      alt={`${activity.title} photo ${index + 1}`}
                      width={300}
                      height={200}
                      className="w-full h-48 object-cover rounded-lg cursor-pointer hover:opacity-90 transition-opacity"
                      onClick={() => setSelectedImageIndex(index)}
                    />
                    <button
                      onClick={() => handleRemovePhoto(imagePath)}
                      className="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                      title="Remove photo"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <ImageIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500 mb-4">
                  {activity.completed ? 'No photos yet' : 'Complete this activity to add photos'}
                </p>
                {activity.completed && (
                  <button
                    onClick={() => fileInputRef.current?.click()}
                    disabled={isUploading}
                    className="inline-flex items-center space-x-2 bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors"
                  >
                    <Camera className="h-4 w-4" />
                    <span>Add First Photo</span>
                  </button>
                )}
              </div>
            )}

            {/* Hidden file input */}
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handlePhotoUpload}
              className="hidden"
            />
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Activity Info */}
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6">
            <h3 className="text-lg font-bold text-gray-800 mb-4">Details</h3>
            <div className="space-y-4">
              {/* Price */}
              <div className="flex items-center space-x-3">
                <DollarSign className="h-5 w-5 text-gray-400" />
                <div>
                  <p className="text-sm text-gray-500">Price</p>
                  <p className="font-semibold text-gray-800">${activity.price.toFixed(2)}</p>
                </div>
              </div>

              {/* Date */}
              {activity.plannedDate && (
                <div className="flex items-center space-x-3">
                  <Calendar className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-500">Planned Date</p>
                    <p className="font-semibold text-gray-800">
                      {format(new Date(activity.plannedDate), 'MMM dd, yyyy')}
                    </p>
                  </div>
                </div>
              )}

              {/* Completed Date */}
              {activity.completedDate && (
                <div className="flex items-center space-x-3">
                  <Check className="h-5 w-5 text-green-500" />
                  <div>
                    <p className="text-sm text-gray-500">Completed</p>
                    <p className="font-semibold text-gray-800">
                      {format(new Date(activity.completedDate), 'MMM dd, yyyy')}
                    </p>
                  </div>
                </div>
              )}

              {/* Location */}
              {activity.location && (
                <div className="flex items-center space-x-3">
                  <MapPin className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-500">Location</p>
                    <p className="font-semibold text-gray-800">{activity.location}</p>
                  </div>
                </div>
              )}

              {/* Duration */}
              {activity.duration && (
                <div className="flex items-center space-x-3">
                  <Clock className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm text-gray-500">Duration</p>
                    <p className="font-semibold text-gray-800">{activity.duration} minutes</p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Rating (only show if completed) */}
          {activity.completed && (
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6">
              <h3 className="text-lg font-bold text-gray-800 mb-4">Rating</h3>
              <div className="flex items-center space-x-1">
                {[1, 2, 3, 4, 5].map((star) => (
                  <button
                    key={star}
                    onClick={() => handleRating(star)}
                    className="transition-colors duration-200 p-1"
                  >
                    <Star
                      className={`h-8 w-8 ${
                        star <= (activity.rating || 0)
                          ? 'text-yellow-400 fill-current'
                          : 'text-gray-300 hover:text-yellow-300'
                      }`}
                    />
                  </button>
                ))}
              </div>
              {activity.rating && (
                <p className="text-sm text-gray-600 mt-2">
                  {activity.rating} out of 5 stars
                </p>
              )}
            </div>
          )}

          {/* Tags */}
          {activity.tags.length > 0 && (
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6">
              <h3 className="text-lg font-bold text-gray-800 mb-4">Tags</h3>
              <div className="flex flex-wrap gap-2">
                {activity.tags.map((tag, index) => (
                  <span
                    key={index}
                    className="px-3 py-1 bg-purple-100 text-purple-700 text-sm rounded-full"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* Notes */}
          {activity.notes && (
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6">
              <h3 className="text-lg font-bold text-gray-800 mb-4">Notes</h3>
              <p className="text-gray-600 leading-relaxed">{activity.notes}</p>
            </div>
          )}
        </div>
      </div>

      {/* Image Modal */}
      {selectedImageIndex !== null && (
        <div className="fixed inset-0 bg-black/90 flex items-center justify-center z-50 p-4">
          <div className="relative max-w-4xl max-h-full">
            <button
              onClick={() => setSelectedImageIndex(null)}
              className="absolute top-4 right-4 p-2 bg-white/20 text-white rounded-full hover:bg-white/30 transition-colors z-10"
            >
              <X className="h-6 w-6" />
            </button>

            <Image
              src={`/api/data/${activity.images[selectedImageIndex]}`}
              alt={`${activity.title} photo ${selectedImageIndex + 1}`}
              width={800}
              height={600}
              className="max-w-full max-h-full object-contain rounded-lg"
            />

            {activity.images.length > 1 && (
              <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                {activity.images.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setSelectedImageIndex(index)}
                    className={`w-3 h-3 rounded-full transition-colors ${
                      index === selectedImageIndex ? 'bg-white' : 'bg-white/50'
                    }`}
                  />
                ))}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl p-6 max-w-sm w-full">
            <h3 className="text-lg font-bold text-gray-800 mb-2">Delete Activity</h3>
            <p className="text-gray-600 mb-6">
              Are you sure you want to delete "{activity.title}"? This action cannot be undone.
            </p>
            <div className="flex space-x-3">
              <button
                onClick={() => setShowDeleteConfirm(false)}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  handleDelete();
                  setShowDeleteConfirm(false);
                }}
                className="flex-1 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
