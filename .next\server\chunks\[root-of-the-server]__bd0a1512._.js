module.exports = {

"[project]/.next-internal/server/app/api/upload/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[project]/src/lib/storage.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "addActivity": (()=>addActivity),
    "addIdea": (()=>addIdea),
    "convertIdeaToActivity": (()=>convertIdeaToActivity),
    "deleteActivity": (()=>deleteActivity),
    "deleteIdea": (()=>deleteIdea),
    "ensureDataDir": (()=>ensureDataDir),
    "readActivities": (()=>readActivities),
    "readIdeas": (()=>readIdeas),
    "saveUploadedFile": (()=>saveUploadedFile),
    "updateActivity": (()=>updateActivity),
    "updateIdea": (()=>updateIdea),
    "writeActivities": (()=>writeActivities),
    "writeIdeas": (()=>writeIdeas)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/fs [external] (fs, cjs)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/path [external] (path, cjs)");
;
;
const DATA_DIR = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(process.cwd(), 'data');
const ACTIVITIES_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(DATA_DIR, 'activities.json');
const IDEAS_FILE = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(DATA_DIR, 'ideas.json');
const UPLOADS_DIR = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(DATA_DIR, 'uploads');
async function ensureDataDir() {
    try {
        await __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["promises"].access(DATA_DIR);
    } catch  {
        await __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["promises"].mkdir(DATA_DIR, {
            recursive: true
        });
    }
    try {
        await __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["promises"].access(UPLOADS_DIR);
    } catch  {
        await __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["promises"].mkdir(UPLOADS_DIR, {
            recursive: true
        });
    }
}
async function readActivities() {
    try {
        await ensureDataDir();
        const data = await __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["promises"].readFile(ACTIVITIES_FILE, 'utf-8');
        const parsed = JSON.parse(data);
        return parsed.activities || [];
    } catch (error) {
        // If file doesn't exist or is invalid, return empty array
        return [];
    }
}
async function writeActivities(activities) {
    await ensureDataDir();
    const data = {
        activities,
        lastUpdated: new Date().toISOString()
    };
    await __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["promises"].writeFile(ACTIVITIES_FILE, JSON.stringify(data, null, 2));
}
async function addActivity(activity) {
    const activities = await readActivities();
    const newActivity = {
        ...activity,
        id: generateId(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    };
    activities.push(newActivity);
    await writeActivities(activities);
    return newActivity;
}
async function updateActivity(id, updates) {
    const activities = await readActivities();
    const index = activities.findIndex((a)=>a.id === id);
    if (index === -1) return null;
    activities[index] = {
        ...activities[index],
        ...updates,
        updatedAt: new Date().toISOString()
    };
    await writeActivities(activities);
    return activities[index];
}
async function deleteActivity(id) {
    const activities = await readActivities();
    const filteredActivities = activities.filter((a)=>a.id !== id);
    if (filteredActivities.length === activities.length) {
        return false; // Activity not found
    }
    await writeActivities(filteredActivities);
    return true;
}
// Simple ID generator
function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
}
async function saveUploadedFile(file) {
    await ensureDataDir();
    const fileName = `${Date.now()}-${file.name}`;
    const filePath = __TURBOPACK__imported__module__$5b$externals$5d2f$path__$5b$external$5d$__$28$path$2c$__cjs$29$__["default"].join(UPLOADS_DIR, fileName);
    const buffer = Buffer.from(await file.arrayBuffer());
    await __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["promises"].writeFile(filePath, buffer);
    return `uploads/${fileName}`;
}
async function readIdeas() {
    try {
        await ensureDataDir();
        const data = await __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["promises"].readFile(IDEAS_FILE, 'utf-8');
        const parsed = JSON.parse(data);
        return parsed.ideas || [];
    } catch (error) {
        // If file doesn't exist or is invalid, return empty array
        return [];
    }
}
async function writeIdeas(ideas) {
    await ensureDataDir();
    const data = {
        ideas,
        lastUpdated: new Date().toISOString()
    };
    await __TURBOPACK__imported__module__$5b$externals$5d2f$fs__$5b$external$5d$__$28$fs$2c$__cjs$29$__["promises"].writeFile(IDEAS_FILE, JSON.stringify(data, null, 2));
}
async function addIdea(idea) {
    const ideas = await readIdeas();
    const newIdea = {
        ...idea,
        id: generateId(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
    };
    ideas.push(newIdea);
    await writeIdeas(ideas);
    return newIdea;
}
async function updateIdea(id, updates) {
    const ideas = await readIdeas();
    const index = ideas.findIndex((i)=>i.id === id);
    if (index === -1) return null;
    ideas[index] = {
        ...ideas[index],
        ...updates,
        updatedAt: new Date().toISOString()
    };
    await writeIdeas(ideas);
    return ideas[index];
}
async function deleteIdea(id) {
    const ideas = await readIdeas();
    const filteredIdeas = ideas.filter((i)=>i.id !== id);
    if (filteredIdeas.length === ideas.length) {
        return false; // Idea not found
    }
    await writeIdeas(filteredIdeas);
    return true;
}
async function convertIdeaToActivity(ideaId, activityData) {
    const ideas = await readIdeas();
    const idea = ideas.find((i)=>i.id === ideaId);
    if (!idea) return null;
    const newActivity = {
        title: idea.title,
        description: idea.description,
        price: idea.estimatedPrice || 0,
        category: idea.category,
        plannedDate: undefined,
        completedDate: undefined,
        images: [],
        completed: false,
        rating: undefined,
        notes: idea.notes,
        tags: idea.tags,
        location: undefined,
        duration: undefined,
        ...activityData
    };
    return await addActivity(newActivity);
}
}}),
"[project]/src/app/api/upload/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$storage$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/storage.ts [app-route] (ecmascript)");
;
;
async function POST(request) {
    try {
        const formData = await request.formData();
        const file = formData.get('file');
        if (!file) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'No file provided'
            }, {
                status: 400
            });
        }
        // Check file type
        const allowedTypes = [
            'image/jpeg',
            'image/jpg',
            'image/png',
            'image/gif',
            'image/webp'
        ];
        if (!allowedTypes.includes(file.type)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Invalid file type. Only images are allowed.'
            }, {
                status: 400
            });
        }
        // Check file size (max 5MB)
        const maxSize = 5 * 1024 * 1024; // 5MB
        if (file.size > maxSize) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'File too large. Maximum size is 5MB.'
            }, {
                status: 400
            });
        }
        const filePath = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$storage$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["saveUploadedFile"])(file);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            filePath
        }, {
            status: 201
        });
    } catch (error) {
        console.error('Error uploading file:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to upload file'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__bd0a1512._.js.map