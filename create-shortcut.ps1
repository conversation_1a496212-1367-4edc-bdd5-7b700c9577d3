# PowerShell script to create a desktop shortcut for Date Planner
# Run this script as Administrator if needed

$WshShell = New-Object -comObject WScript.Shell
$Shortcut = $WshShell.CreateShortcut("$Home\Desktop\💕 Date Planner.lnk")
$Shortcut.TargetPath = "$PSScriptRoot\start-date-planner.bat"
$Shortcut.WorkingDirectory = $PSScriptRoot
$Shortcut.Description = "Personal Date Ideas & Activities Planner"
$Shortcut.WindowStyle = 1  # Normal window

# Try to use a heart icon from system icons
# You can replace this with a custom .ico file path if you have one
$Shortcut.IconLocation = "shell32.dll,23"  # Heart-like icon from system

$Shortcut.Save()

Write-Host "✅ Desktop shortcut created successfully!" -ForegroundColor Green
Write-Host "📍 Location: $Home\Desktop\💕 Date Planner.lnk" -ForegroundColor Cyan
Write-Host ""
Write-Host "🎨 To customize the icon further:" -ForegroundColor Yellow
Write-Host "   1. Right-click the shortcut on your desktop" -ForegroundColor White
Write-Host "   2. Select 'Properties'" -ForegroundColor White
Write-Host "   3. Click 'Change Icon...'" -ForegroundColor White
Write-Host "   4. Browse for a custom .ico file or choose from system icons" -ForegroundColor White
Write-Host ""
Write-Host "💡 You can also rename the shortcut to anything you like!" -ForegroundColor Magenta

# Pause to show the message
Read-Host "Press Enter to continue..."
