# PowerShell script to create a desktop shortcut for Date Planner
# Run this script as Administrator if needed

Write-Host "Creating desktop shortcut for Date Planner..." -ForegroundColor Cyan
Write-Host ""

try {
    $WshShell = New-Object -comObject WScript.Shell

    # Try with emoji first, fallback to regular name if it fails
    $ShortcutPath = "$Home\Desktop\Date Planner.lnk"

    Write-Host "Creating shortcut at: $ShortcutPath" -ForegroundColor Gray

    $Shortcut = $WshShell.CreateShortcut($ShortcutPath)
    $Shortcut.TargetPath = "$PSScriptRoot\start-date-planner.bat"
    $Shortcut.WorkingDirectory = $PSScriptRoot
    $Shortcut.Description = "Personal Date Ideas & Activities Planner"
    $Shortcut.WindowStyle = 1  # Normal window

    # Try to use a heart icon from system icons
    # You can replace this with a custom .ico file path if you have one
    $Shortcut.IconLocation = "shell32.dll,23"  # Heart-like icon from system

    $Shortcut.Save()

    Write-Host "✅ Desktop shortcut created successfully!" -ForegroundColor Green

    Write-Host "📍 Location: $ShortcutPath" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "🎨 To customize the icon further:" -ForegroundColor Yellow
    Write-Host "   1. Right-click the shortcut on your desktop" -ForegroundColor White
    Write-Host "   2. Select 'Properties'" -ForegroundColor White
    Write-Host "   3. Click 'Change Icon...'" -ForegroundColor White
    Write-Host "   4. Browse for a custom .ico file or choose from system icons" -ForegroundColor White
    Write-Host ""
    Write-Host "💡 You can also rename the shortcut to anything you like!" -ForegroundColor Magenta
    Write-Host "   (Right-click → Rename, then add emojis like: 💕 Date Planner)" -ForegroundColor Magenta

} catch {
    Write-Host "❌ Error creating shortcut: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "🔧 Manual steps to create shortcut:" -ForegroundColor Yellow
    Write-Host "   1. Right-click on 'start-date-planner.bat'" -ForegroundColor White
    Write-Host "   2. Select 'Create shortcut'" -ForegroundColor White
    Write-Host "   3. Drag the shortcut to your desktop" -ForegroundColor White
    Write-Host "   4. Right-click the shortcut → Properties → Change name" -ForegroundColor White
}

Write-Host ""
Write-Host "🚀 To test: Double-click the shortcut or the start-date-planner.bat file" -ForegroundColor Green

# Pause to show the message
Read-Host "Press Enter to continue..."
