'use client';

import { useState, useEffect } from 'react';
import { Activity } from '@/types';
import ActivityCard from '@/components/ActivityCard';
import ActivityFilters from '@/components/ActivityFilters';
import { Search, Plus, Heart } from 'lucide-react';
import Link from 'next/link';

export default function Home() {
  const [activities, setActivities] = useState<Activity[]>([]);
  const [filteredActivities, setFilteredActivities] = useState<Activity[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    fetchActivities();
  }, []);

  useEffect(() => {
    // Filter activities based on search term
    if (searchTerm) {
      const filtered = activities.filter(activity =>
        activity.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        activity.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        activity.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      );
      setFilteredActivities(filtered);
    } else {
      setFilteredActivities(activities);
    }
  }, [activities, searchTerm]);

  const fetchActivities = async () => {
    try {
      const response = await fetch('/api/activities');
      const data = await response.json();
      setActivities(data.activities || []);
    } catch (error) {
      console.error('Error fetching activities:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleActivityUpdate = (updatedActivity: Activity) => {
    setActivities(prev =>
      prev.map(activity =>
        activity.id === updatedActivity.id ? updatedActivity : activity
      )
    );
  };

  const handleActivityDelete = (deletedId: string) => {
    setActivities(prev => prev.filter(activity => activity.id !== deletedId));
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-pulse-slow">
          <Heart className="h-12 w-12 text-purple-500" />
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-purple-600 via-pink-600 to-orange-500 bg-clip-text text-transparent">
          Your Date Ideas
        </h1>
        <p className="text-gray-600 text-lg max-w-2xl mx-auto">
          Plan, organize, and cherish your perfect moments together
        </p>
      </div>

      {/* Search and Add Button */}
      <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
          <input
            type="text"
            placeholder="Search activities..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 bg-white/80 backdrop-blur-sm"
          />
        </div>

        <Link
          href="/add"
          className="flex items-center space-x-2 bg-gradient-to-r from-purple-500 to-pink-500 text-white px-6 py-3 rounded-xl hover:from-purple-600 hover:to-pink-600 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
        >
          <Plus className="h-5 w-5" />
          <span className="font-medium">Add Activity</span>
        </Link>
      </div>

      {/* Activities Grid */}
      {filteredActivities.length === 0 ? (
        <div className="text-center py-16">
          <Heart className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-600 mb-2">
            {activities.length === 0 ? 'No activities yet' : 'No activities found'}
          </h3>
          <p className="text-gray-500 mb-6">
            {activities.length === 0
              ? 'Start planning your perfect dates and activities!'
              : 'Try adjusting your search terms'
            }
          </p>
          {activities.length === 0 && (
            <Link
              href="/add"
              className="inline-flex items-center space-x-2 bg-gradient-to-r from-purple-500 to-pink-500 text-white px-6 py-3 rounded-xl hover:from-purple-600 hover:to-pink-600 transition-all duration-200"
            >
              <Plus className="h-5 w-5" />
              <span>Add Your First Activity</span>
            </Link>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredActivities.map((activity, index) => (
            <div key={activity.id} className="animate-fade-in-up" style={{ animationDelay: `${index * 0.1}s` }}>
              <ActivityCard
                activity={activity}
                onUpdate={handleActivityUpdate}
                onDelete={handleActivityDelete}
              />
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
