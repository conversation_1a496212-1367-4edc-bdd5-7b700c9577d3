'use client';

import { useState, useEffect } from 'react';
import { Activity } from '@/types';
import ActivityCard from '@/components/ActivityCard';
import { Calendar, Plus, Heart, Lightbulb } from 'lucide-react';
import Link from 'next/link';
import { isAfter, startOfDay } from 'date-fns';

// Prevent hydration issues by ensuring client-side only rendering for dynamic content
function useIsClient() {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  return isClient;
}

export default function Home() {
  const [upcomingActivities, setUpcomingActivities] = useState<Activity[]>([]);
  const [loading, setLoading] = useState(true);
  const isClient = useIsClient();

  useEffect(() => {
    if (isClient) {
      fetchUpcomingActivities();
    }
  }, [isClient]);

  const fetchUpcomingActivities = async () => {
    try {
      const response = await fetch('/api/activities');
      const data = await response.json();
      const allActivities = data.activities || [];

      // Filter for upcoming activities (not completed and either no date or future date)
      const today = startOfDay(new Date());
      const upcoming = allActivities.filter((activity: Activity) => {
        if (activity.completed) return false;
        if (!activity.plannedDate) return true; // Activities without dates are considered upcoming
        return isAfter(new Date(activity.plannedDate), today) ||
               startOfDay(new Date(activity.plannedDate)).getTime() === today.getTime();
      });

      // Sort by planned date (soonest first), then by created date
      upcoming.sort((a: Activity, b: Activity) => {
        if (a.plannedDate && b.plannedDate) {
          return new Date(a.plannedDate).getTime() - new Date(b.plannedDate).getTime();
        }
        if (a.plannedDate && !b.plannedDate) return -1;
        if (!a.plannedDate && b.plannedDate) return 1;
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      });

      setUpcomingActivities(upcoming.slice(0, 6)); // Show only next 6 activities
    } catch (error) {
      console.error('Error fetching activities:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleActivityUpdate = (updatedActivity: Activity) => {
    // Refresh the upcoming activities list
    fetchUpcomingActivities();
  };

  const handleActivityDelete = (deletedId: string) => {
    // Refresh the upcoming activities list
    fetchUpcomingActivities();
  };

  if (!isClient || loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-pulse-slow">
          <Heart className="h-12 w-12 text-purple-500" />
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-purple-600 via-pink-600 to-orange-500 bg-clip-text text-transparent">
          Upcoming Activities
        </h1>
        <p className="text-gray-600 text-lg max-w-2xl mx-auto">
          Your next planned dates and activities
        </p>
      </div>

      {/* Quick Actions */}
      <div className="flex flex-col sm:flex-row gap-4 items-center justify-center">
        <Link
          href="/ideas"
          className="flex items-center space-x-2 bg-gradient-to-r from-yellow-500 to-orange-500 text-white px-6 py-3 rounded-xl hover:from-yellow-600 hover:to-orange-600 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
        >
          <Lightbulb className="h-5 w-5" />
          <span className="font-medium">Browse Ideas</span>
        </Link>

        <Link
          href="/activities"
          className="flex items-center space-x-2 bg-gradient-to-r from-purple-500 to-pink-500 text-white px-6 py-3 rounded-xl hover:from-purple-600 hover:to-pink-600 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
        >
          <Plus className="h-5 w-5" />
          <span className="font-medium">Plan Activity</span>
        </Link>

        <Link
          href="/calendar"
          className="flex items-center space-x-2 bg-gradient-to-r from-blue-500 to-indigo-500 text-white px-6 py-3 rounded-xl hover:from-blue-600 hover:to-indigo-600 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
        >
          <Calendar className="h-5 w-5" />
          <span className="font-medium">View Calendar</span>
        </Link>
      </div>

      {/* Upcoming Activities */}
      {upcomingActivities.length === 0 ? (
        <div className="text-center py-16">
          <Heart className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-600 mb-2">
            No upcoming activities
          </h3>
          <p className="text-gray-500 mb-6">
            Start by browsing ideas or planning your next activity!
          </p>
          <div className="flex flex-col sm:flex-row gap-4 items-center justify-center">
            <Link
              href="/ideas"
              className="inline-flex items-center space-x-2 bg-gradient-to-r from-yellow-500 to-orange-500 text-white px-6 py-3 rounded-xl hover:from-yellow-600 hover:to-orange-600 transition-all duration-200"
            >
              <Lightbulb className="h-5 w-5" />
              <span>Get Ideas</span>
            </Link>
            <Link
              href="/activities"
              className="inline-flex items-center space-x-2 bg-gradient-to-r from-purple-500 to-pink-500 text-white px-6 py-3 rounded-xl hover:from-purple-600 hover:to-pink-600 transition-all duration-200"
            >
              <Plus className="h-5 w-5" />
              <span>Plan Activity</span>
            </Link>
          </div>
        </div>
      ) : (
        <>
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-800">Next 6 Activities</h2>
            <Link
              href="/activities"
              className="text-purple-600 hover:text-purple-700 font-medium transition-colors"
            >
              View All →
            </Link>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {upcomingActivities.map((activity, index) => (
              <div key={activity.id} className="animate-fade-in-up" style={{ animationDelay: `${index * 0.1}s` }}>
                <ActivityCard
                  activity={activity}
                  onUpdate={handleActivityUpdate}
                  onDelete={handleActivityDelete}
                />
              </div>
            ))}
          </div>
        </>
      )}
    </div>
  );
}
