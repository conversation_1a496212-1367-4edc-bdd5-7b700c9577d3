export interface Activity {
  id: string;
  title: string;
  description: string;
  price: number;
  category: ActivityCategory;
  plannedDate?: string; // ISO date string
  completedDate?: string; // ISO date string
  images: string[]; // file paths
  completed: boolean;
  rating?: number; // 1-5 stars
  notes: string;
  tags: string[];
  location?: string;
  duration?: number; // in minutes
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
}

export type ActivityCategory = 
  | 'dining'
  | 'entertainment'
  | 'outdoor'
  | 'cultural'
  | 'romantic'
  | 'adventure'
  | 'relaxation'
  | 'travel'
  | 'sports'
  | 'other';

export interface ActivityFormData {
  title: string;
  description: string;
  price: number;
  category: ActivityCategory;
  plannedDate?: string;
  location?: string;
  duration?: number;
  tags: string;
  notes: string;
}

export interface ActivityFilters {
  category?: ActivityCategory;
  priceRange?: [number, number];
  completed?: boolean;
  dateRange?: [string, string];
  search?: string;
}
