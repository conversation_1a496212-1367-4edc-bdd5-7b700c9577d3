@echo off
title Date Planner - Starting...

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    💕 Date Planner App 💕                    ║
echo ║                      Starting up...                          ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM Change to the app directory
cd /d "%~dp0"

REM Check if node_modules exists
if not exist "node_modules" (
    echo Installing dependencies...
    npm install
    echo.
)

echo Starting the Date Planner server...
echo.
echo ⏳ Please wait while the app loads...
echo 🌐 The app will open in your browser automatically
echo 📍 URL: http://localhost:3000
echo.
echo ⚠️  Keep this window open while using the app
echo ❌ Close this window to stop the app
echo.

REM Start the Next.js development server in the background
start /B npm run dev

REM Wait a few seconds for the server to start
timeout /t 5 /nobreak >nul

REM Open the browser
start http://localhost:3000

REM Keep the window open and show status
echo.
echo ✅ Date Planner is now running!
echo 🌐 Browser should have opened automatically
echo 📱 If not, go to: http://localhost:3000
echo.
echo Press any key to stop the app and close this window...
pause >nul

REM Kill the Node.js process when user presses a key
taskkill /f /im node.exe >nul 2>&1
echo.
echo 👋 Date Planner stopped. Have a great day!
timeout /t 2 >nul
