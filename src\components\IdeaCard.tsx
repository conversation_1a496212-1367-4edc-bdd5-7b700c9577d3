'use client';

import { useState, useEffect } from 'react';
import { Idea } from '@/types';
import { 
  DollarSign, 
  Edit, 
  Trash2, 
  MessageCircle,
  CheckCircle,
  XCircle,
  Clock,
  Calendar,
  ArrowRight
} from 'lucide-react';
import { useRouter } from 'next/navigation';

interface IdeaCardProps {
  idea: Idea;
  onUpdate: (idea: Idea) => void;
  onDelete: (id: string) => void;
}

export default function IdeaCard({ idea, onUpdate, onDelete }: IdeaCardProps) {
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showProposalModal, setShowProposalModal] = useState(false);
  const [showResponseModal, setShowResponseModal] = useState(false);
  const [responseNotes, setResponseNotes] = useState(idea.responseNotes || '');
  const [isClient, setIsClient] = useState(false);
  const router = useRouter();

  useEffect(() => {
    setIsClient(true);
  }, []);

  const handlePropose = async () => {
    try {
      const updatedIdea = { ...idea, proposed: true };

      const response = await fetch('/api/ideas', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updatedIdea),
      });

      if (response.ok) {
        const data = await response.json();
        onUpdate(data.idea);
        setShowProposalModal(false);
      }
    } catch (error) {
      console.error('Error proposing idea:', error);
    }
  };

  const handleResponse = async (accepted: boolean) => {
    try {
      const updatedIdea = { 
        ...idea, 
        accepted, 
        responseNotes: responseNotes.trim() || undefined 
      };

      const response = await fetch('/api/ideas', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updatedIdea),
      });

      if (response.ok) {
        const data = await response.json();
        onUpdate(data.idea);
        setShowResponseModal(false);
        setResponseNotes('');
      }
    } catch (error) {
      console.error('Error updating response:', error);
    }
  };

  const handleConvertToActivity = async () => {
    try {
      const response = await fetch('/api/ideas/convert', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          ideaId: idea.id,
          price: idea.estimatedPrice || 0,
        }),
      });

      if (response.ok) {
        router.push('/activities');
      }
    } catch (error) {
      console.error('Error converting to activity:', error);
    }
  };

  const handleDelete = async () => {
    try {
      const response = await fetch(`/api/ideas?id=${idea.id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        onDelete(idea.id);
      }
    } catch (error) {
      console.error('Error deleting idea:', error);
    }
  };

  const getCategoryColor = (category: string) => {
    const colors = {
      dining: 'from-orange-400 to-red-500',
      entertainment: 'from-purple-400 to-pink-500',
      outdoor: 'from-green-400 to-blue-500',
      cultural: 'from-indigo-400 to-purple-500',
      romantic: 'from-pink-400 to-rose-500',
      adventure: 'from-yellow-400 to-orange-500',
      relaxation: 'from-blue-400 to-indigo-500',
      travel: 'from-teal-400 to-cyan-500',
      sports: 'from-red-400 to-pink-500',
      other: 'from-gray-400 to-gray-500',
    };
    return colors[category as keyof typeof colors] || colors.other;
  };

  const getStatusInfo = () => {
    if (!idea.proposed) {
      return { color: 'bg-blue-100 text-blue-700', text: 'Not Proposed', icon: Clock };
    }
    if (idea.accepted === undefined) {
      return { color: 'bg-yellow-100 text-yellow-700', text: 'Waiting Response', icon: MessageCircle };
    }
    if (idea.accepted) {
      return { color: 'bg-green-100 text-green-700', text: 'Accepted', icon: CheckCircle };
    }
    return { color: 'bg-red-100 text-red-700', text: 'Rejected', icon: XCircle };
  };

  const statusInfo = getStatusInfo();
  const StatusIcon = statusInfo.icon;

  return (
    <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-white/20">
      {/* Header with category badge */}
      <div className={`h-2 bg-gradient-to-r ${getCategoryColor(idea.category)}`} />
      
      <div className="p-6">
        {/* Title and Status */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <h3 className="text-xl font-bold text-gray-800 mb-2">
              {idea.title}
            </h3>
            <p className="text-gray-600 text-sm leading-relaxed">
              {idea.description}
            </p>
          </div>
          
          <div className={`ml-4 px-3 py-1 rounded-full text-xs font-medium flex items-center space-x-1 ${statusInfo.color}`}>
            <StatusIcon className="h-3 w-3" />
            <span>{statusInfo.text}</span>
          </div>
        </div>

        {/* Details */}
        <div className="space-y-3 mb-4">
          {/* Price */}
          {idea.estimatedPrice !== undefined && (
            <div className="flex items-center space-x-2 text-gray-600">
              <DollarSign className="h-4 w-4" />
              <span className="font-medium">~${idea.estimatedPrice.toFixed(2)}</span>
            </div>
          )}
        </div>

        {/* Tags */}
        {idea.tags.length > 0 && (
          <div className="flex flex-wrap gap-2 mb-4">
            {idea.tags.map((tag, index) => (
              <span
                key={index}
                className="px-2 py-1 bg-yellow-100 text-yellow-700 text-xs rounded-full"
              >
                {tag}
              </span>
            ))}
          </div>
        )}

        {/* Response Notes */}
        {idea.responseNotes && (
          <div className="mb-4 p-3 bg-gray-50 rounded-lg">
            <p className="text-sm text-gray-600 italic">"{idea.responseNotes}"</p>
          </div>
        )}

        {/* Actions */}
        <div className="flex items-center justify-between pt-4 border-t border-gray-100">
          <div className="flex items-center space-x-2">
            {!idea.proposed && (
              <button
                onClick={() => setShowProposalModal(true)}
                className="px-3 py-1 bg-blue-100 text-blue-700 text-sm rounded-lg hover:bg-blue-200 transition-colors"
              >
                Propose
              </button>
            )}
            
            {idea.proposed && idea.accepted === undefined && (
              <button
                onClick={() => setShowResponseModal(true)}
                className="px-3 py-1 bg-yellow-100 text-yellow-700 text-sm rounded-lg hover:bg-yellow-200 transition-colors"
              >
                Add Response
              </button>
            )}
            
            {idea.accepted && (
              <button
                onClick={handleConvertToActivity}
                className="flex items-center space-x-1 px-3 py-1 bg-green-100 text-green-700 text-sm rounded-lg hover:bg-green-200 transition-colors"
              >
                <Calendar className="h-3 w-3" />
                <span>Plan It</span>
                <ArrowRight className="h-3 w-3" />
              </button>
            )}
          </div>

          <div className="flex items-center space-x-2">
            <button
              onClick={() => setShowDeleteConfirm(true)}
              className="p-2 rounded-full bg-gray-100 text-gray-400 hover:bg-red-100 hover:text-red-600 transition-all duration-200"
              title="Delete idea"
            >
              <Trash2 className="h-4 w-4" />
            </button>
          </div>
        </div>

        {/* Category Badge */}
        <div className="flex justify-between items-center mt-4">
          <span className={`px-3 py-1 rounded-full text-xs font-medium text-white bg-gradient-to-r ${getCategoryColor(idea.category)}`}>
            {idea.category.charAt(0).toUpperCase() + idea.category.slice(1)}
          </span>
        </div>
      </div>

      {/* Proposal Modal */}
      {showProposalModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl p-6 max-w-sm w-full">
            <h3 className="text-lg font-bold text-gray-800 mb-2">Propose This Idea</h3>
            <p className="text-gray-600 mb-6">
              Mark this idea as proposed to your girlfriend. You can add her response later.
            </p>
            <div className="flex space-x-3">
              <button
                onClick={() => setShowProposalModal(false)}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handlePropose}
                className="flex-1 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
              >
                Propose
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Response Modal */}
      {showResponseModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl p-6 max-w-md w-full">
            <h3 className="text-lg font-bold text-gray-800 mb-4">Add Her Response</h3>
            
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Her thoughts/feedback (optional)
              </label>
              <textarea
                value={responseNotes}
                onChange={(e) => setResponseNotes(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent resize-none"
                rows={3}
                placeholder="What did she say about this idea?"
              />
            </div>
            
            <div className="flex space-x-3">
              <button
                onClick={() => setShowResponseModal(false)}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={() => handleResponse(false)}
                className="flex-1 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
              >
                Rejected
              </button>
              <button
                onClick={() => handleResponse(true)}
                className="flex-1 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
              >
                Accepted
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl p-6 max-w-sm w-full">
            <h3 className="text-lg font-bold text-gray-800 mb-2">Delete Idea</h3>
            <p className="text-gray-600 mb-6">
              Are you sure you want to delete "{idea.title}"? This action cannot be undone.
            </p>
            <div className="flex space-x-3">
              <button
                onClick={() => setShowDeleteConfirm(false)}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  handleDelete();
                  setShowDeleteConfirm(false);
                }}
                className="flex-1 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
