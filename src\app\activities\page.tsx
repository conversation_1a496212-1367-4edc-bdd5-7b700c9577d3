'use client';

import { useState, useEffect } from 'react';
import { Activity } from '@/types';
import ActivityCard from '@/components/ActivityCard';
import { Search, Plus, Activity as ActivityIcon, Calendar, CheckCircle } from 'lucide-react';
import Link from 'next/link';
import { isAfter, startOfDay } from 'date-fns';

// Prevent hydration issues
function useIsClient() {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  return isClient;
}

export default function Activities() {
  const [activities, setActivities] = useState<Activity[]>([]);
  const [upcomingActivities, setUpcomingActivities] = useState<Activity[]>([]);
  const [completedActivities, setCompletedActivities] = useState<Activity[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState<'upcoming' | 'completed' | 'all'>('upcoming');
  const isClient = useIsClient();

  useEffect(() => {
    if (isClient) {
      fetchActivities();
    }
  }, [isClient]);

  useEffect(() => {
    // Separate activities into upcoming and completed
    const today = startOfDay(new Date());
    
    const upcoming = activities.filter(activity => {
      if (activity.completed) return false;
      if (!activity.plannedDate) return true;
      return isAfter(new Date(activity.plannedDate), today) || 
             startOfDay(new Date(activity.plannedDate)).getTime() === today.getTime();
    });

    const completed = activities.filter(activity => activity.completed);

    // Apply search filter
    const filterBySearch = (activityList: Activity[]) => {
      if (!searchTerm) return activityList;
      return activityList.filter(activity =>
        activity.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        activity.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        activity.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    };

    // Sort upcoming by planned date (soonest first)
    upcoming.sort((a, b) => {
      if (a.plannedDate && b.plannedDate) {
        return new Date(a.plannedDate).getTime() - new Date(b.plannedDate).getTime();
      }
      if (a.plannedDate && !b.plannedDate) return -1;
      if (!a.plannedDate && b.plannedDate) return 1;
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });

    // Sort completed by completion date (most recent first)
    completed.sort((a, b) => {
      if (a.completedDate && b.completedDate) {
        return new Date(b.completedDate).getTime() - new Date(a.completedDate).getTime();
      }
      return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();
    });

    setUpcomingActivities(filterBySearch(upcoming));
    setCompletedActivities(filterBySearch(completed));
  }, [activities, searchTerm]);

  const fetchActivities = async () => {
    try {
      const response = await fetch('/api/activities');
      const data = await response.json();
      setActivities(data.activities || []);
    } catch (error) {
      console.error('Error fetching activities:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleActivityUpdate = (updatedActivity: Activity) => {
    setActivities(prev => 
      prev.map(activity => 
        activity.id === updatedActivity.id ? updatedActivity : activity
      )
    );
  };

  const handleActivityDelete = (deletedId: string) => {
    setActivities(prev => prev.filter(activity => activity.id !== deletedId));
  };

  if (!isClient || loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-pulse-slow">
          <ActivityIcon className="h-12 w-12 text-purple-500" />
        </div>
      </div>
    );
  }

  const getCurrentActivities = () => {
    switch (activeTab) {
      case 'upcoming':
        return upcomingActivities;
      case 'completed':
        return completedActivities;
      case 'all':
        // Filter all activities by search term
        const filtered = searchTerm
          ? activities.filter(activity =>
              activity.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
              activity.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
              activity.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
            )
          : activities;

        // Sort all activities by created date (newest first)
        return filtered.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
      default:
        return [];
    }
  };

  const currentActivities = getCurrentActivities();

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-purple-600 via-pink-600 to-orange-500 bg-clip-text text-transparent">
          Activities
        </h1>
        <p className="text-gray-600 text-lg max-w-2xl mx-auto">
          Manage your planned and completed activities
        </p>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="bg-white/80 backdrop-blur-sm rounded-xl p-4 text-center border border-white/20">
          <div className="text-2xl font-bold text-gray-800">{activities.length}</div>
          <div className="text-sm text-gray-600">Total Activities</div>
        </div>
        <div className="bg-blue-50/80 backdrop-blur-sm rounded-xl p-4 text-center border border-blue-200/20">
          <div className="text-2xl font-bold text-blue-600">{upcomingActivities.length}</div>
          <div className="text-sm text-blue-600">Upcoming</div>
        </div>
        <div className="bg-green-50/80 backdrop-blur-sm rounded-xl p-4 text-center border border-green-200/20">
          <div className="text-2xl font-bold text-green-600">{completedActivities.length}</div>
          <div className="text-sm text-green-600">Completed</div>
        </div>
        <div className="bg-purple-50/80 backdrop-blur-sm rounded-xl p-4 text-center border border-purple-200/20">
          <div className="text-2xl font-bold text-purple-600">
            ${activities.reduce((sum, a) => sum + a.price, 0).toFixed(0)}
          </div>
          <div className="text-sm text-purple-600">Total Spent</div>
        </div>
      </div>

      {/* Search and Add Button */}
      <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
          <input
            type="text"
            placeholder="Search activities..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 bg-white/80 backdrop-blur-sm"
          />
        </div>
        
        <Link
          href="/add"
          className="flex items-center space-x-2 bg-gradient-to-r from-purple-500 to-pink-500 text-white px-6 py-3 rounded-xl hover:from-purple-600 hover:to-pink-600 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
        >
          <Plus className="h-5 w-5" />
          <span className="font-medium">Add Activity</span>
        </Link>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 bg-white/80 backdrop-blur-sm rounded-xl p-1 border border-white/20">
        <button
          onClick={() => setActiveTab('upcoming')}
          className={`flex-1 flex items-center justify-center space-x-2 px-4 py-3 rounded-lg transition-all duration-200 ${
            activeTab === 'upcoming'
              ? 'bg-gradient-to-r from-blue-500 to-indigo-500 text-white shadow-lg'
              : 'text-gray-600 hover:text-blue-600 hover:bg-blue-50'
          }`}
        >
          <Calendar className="h-4 w-4" />
          <span className="font-medium">Upcoming ({upcomingActivities.length})</span>
        </button>
        <button
          onClick={() => setActiveTab('completed')}
          className={`flex-1 flex items-center justify-center space-x-2 px-4 py-3 rounded-lg transition-all duration-200 ${
            activeTab === 'completed'
              ? 'bg-gradient-to-r from-green-500 to-emerald-500 text-white shadow-lg'
              : 'text-gray-600 hover:text-green-600 hover:bg-green-50'
          }`}
        >
          <CheckCircle className="h-4 w-4" />
          <span className="font-medium">Completed ({completedActivities.length})</span>
        </button>
        <button
          onClick={() => setActiveTab('all')}
          className={`flex-1 flex items-center justify-center space-x-2 px-4 py-3 rounded-lg transition-all duration-200 ${
            activeTab === 'all'
              ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg'
              : 'text-gray-600 hover:text-purple-600 hover:bg-purple-50'
          }`}
        >
          <ActivityIcon className="h-4 w-4" />
          <span className="font-medium">All ({activities.length})</span>
        </button>
      </div>

      {/* Activities Grid */}
      {currentActivities.length === 0 ? (
        <div className="text-center py-16">
          {activeTab === 'upcoming' ? (
            <>
              <Calendar className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-600 mb-2">
                {activities.length === 0 ? 'No activities yet' : 'No upcoming activities'}
              </h3>
              <p className="text-gray-500 mb-6">
                {activities.length === 0 
                  ? 'Start planning your perfect dates and activities!'
                  : searchTerm 
                    ? 'Try adjusting your search terms'
                    : 'All your activities are completed!'
                }
              </p>
              {activities.length === 0 && (
                <Link
                  href="/add"
                  className="inline-flex items-center space-x-2 bg-gradient-to-r from-purple-500 to-pink-500 text-white px-6 py-3 rounded-xl hover:from-purple-600 hover:to-pink-600 transition-all duration-200"
                >
                  <Plus className="h-5 w-5" />
                  <span>Add Your First Activity</span>
                </Link>
              )}
            </>
          ) : (
            <>
              <CheckCircle className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-600 mb-2">
                No completed activities
              </h3>
              <p className="text-gray-500 mb-6">
                {searchTerm 
                  ? 'Try adjusting your search terms'
                  : 'Complete some activities to see them here!'
                }
              </p>
            </>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {currentActivities.map((activity, index) => (
            <div key={activity.id} className="animate-fade-in-up" style={{ animationDelay: `${index * 0.1}s` }}>
              <ActivityCard
                activity={activity}
                onUpdate={handleActivityUpdate}
                onDelete={handleActivityDelete}
              />
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
