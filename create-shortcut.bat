@echo off
title Create Date Planner Shortcut

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                Creating Desktop Shortcut...                  ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM Get current directory
set "CURRENT_DIR=%~dp0"

REM Create VBS script to make shortcut
echo Set oWS = WScript.CreateObject("WScript.Shell") > "%TEMP%\CreateShortcut.vbs"
echo sLinkFile = "%USERPROFILE%\Desktop\Date Planner.lnk" >> "%TEMP%\CreateShortcut.vbs"
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> "%TEMP%\CreateShortcut.vbs"
echo oLink.TargetPath = "%CURRENT_DIR%start-date-planner.bat" >> "%TEMP%\CreateShortcut.vbs"
echo oLink.WorkingDirectory = "%CURRENT_DIR%" >> "%TEMP%\CreateShortcut.vbs"
echo oLink.Description = "Personal Date Ideas & Activities Planner" >> "%TEMP%\CreateShortcut.vbs"
echo oLink.IconLocation = "shell32.dll,23" >> "%TEMP%\CreateShortcut.vbs"
echo oLink.Save >> "%TEMP%\CreateShortcut.vbs"

REM Run the VBS script
cscript //nologo "%TEMP%\CreateShortcut.vbs"

REM Clean up
del "%TEMP%\CreateShortcut.vbs"

if exist "%USERPROFILE%\Desktop\Date Planner.lnk" (
    echo ✅ Desktop shortcut created successfully!
    echo 📍 Location: %USERPROFILE%\Desktop\Date Planner.lnk
    echo.
    echo 🎨 To customize the icon:
    echo    1. Right-click the shortcut on your desktop
    echo    2. Select "Properties"
    echo    3. Click "Change Icon..."
    echo    4. Browse for a custom .ico file or choose from system icons
    echo.
    echo 💡 You can rename the shortcut to add emojis like: 💕 Date Planner
) else (
    echo ❌ Failed to create shortcut automatically
    echo.
    echo 🔧 Manual steps:
    echo    1. Right-click on "start-date-planner.bat"
    echo    2. Select "Create shortcut"
    echo    3. Drag the shortcut to your desktop
    echo    4. Right-click the shortcut → Properties → Change name
)

echo.
echo 🚀 To test: Double-click the shortcut or start-date-planner.bat
echo.
pause
