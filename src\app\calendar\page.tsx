'use client';

import { useState, useEffect } from 'react';
import { Activity } from '@/types';
import { format, startOfMonth, endOfMonth, eachDayOfInterval, isSameDay, isToday } from 'date-fns';
import { ChevronLeft, ChevronRight, Calendar as CalendarIcon, Heart, Plus } from 'lucide-react';
import QuickActivityModal from '@/components/QuickActivityModal';

// Prevent hydration issues
function useIsClient() {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  return isClient;
}

export default function Calendar() {
  const [activities, setActivities] = useState<Activity[]>([]);
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [loading, setLoading] = useState(true);
  const [showQuickAdd, setShowQuickAdd] = useState(false);
  const [quickAddDate, setQuickAddDate] = useState<Date | null>(null);
  const isClient = useIsClient();

  useEffect(() => {
    if (isClient) {
      fetchActivities();
    }
  }, [isClient]);

  const fetchActivities = async () => {
    try {
      const response = await fetch('/api/activities');
      const data = await response.json();
      setActivities(data.activities || []);
    } catch (error) {
      console.error('Error fetching activities:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDateClick = (date: Date) => {
    setSelectedDate(date);
  };

  const handleDateDoubleClick = (date: Date) => {
    setQuickAddDate(date);
    setShowQuickAdd(true);
  };

  const handleActivityCreated = (newActivity: Activity) => {
    setActivities(prev => [...prev, newActivity]);
    setShowQuickAdd(false);
    setQuickAddDate(null);
  };

  const monthStart = startOfMonth(currentDate);
  const monthEnd = endOfMonth(currentDate);
  const monthDays = eachDayOfInterval({ start: monthStart, end: monthEnd });

  // Get activities for a specific date
  const getActivitiesForDate = (date: Date) => {
    return activities.filter(activity => 
      activity.plannedDate && isSameDay(new Date(activity.plannedDate), date)
    );
  };

  // Get activities for selected date
  const selectedDateActivities = selectedDate ? getActivitiesForDate(selectedDate) : [];

  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentDate(prev => {
      const newDate = new Date(prev);
      if (direction === 'prev') {
        newDate.setMonth(prev.getMonth() - 1);
      } else {
        newDate.setMonth(prev.getMonth() + 1);
      }
      return newDate;
    });
  };

  const getCategoryColor = (category: string) => {
    const colors = {
      dining: 'bg-orange-500',
      entertainment: 'bg-purple-500',
      outdoor: 'bg-green-500',
      cultural: 'bg-indigo-500',
      romantic: 'bg-pink-500',
      adventure: 'bg-yellow-500',
      relaxation: 'bg-blue-500',
      travel: 'bg-teal-500',
      sports: 'bg-red-500',
      other: 'bg-gray-500',
    };
    return colors[category as keyof typeof colors] || colors.other;
  };

  if (!isClient || loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-pulse-slow">
          <CalendarIcon className="h-12 w-12 text-purple-500" />
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-purple-600 via-pink-600 to-orange-500 bg-clip-text text-transparent">
          Activity Calendar
        </h1>
        <p className="text-gray-600 text-lg max-w-2xl mx-auto">
          View and plan your activities by date
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Calendar */}
        <div className="lg:col-span-2">
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6">
            {/* Calendar Header */}
            <div className="flex items-center justify-between mb-6">
              <button
                onClick={() => navigateMonth('prev')}
                className="p-2 rounded-full hover:bg-purple-100 text-purple-600 transition-colors"
              >
                <ChevronLeft className="h-5 w-5" />
              </button>
              
              <h2 className="text-xl font-bold text-gray-800">
                {format(currentDate, 'MMMM yyyy')}
              </h2>
              
              <button
                onClick={() => navigateMonth('next')}
                className="p-2 rounded-full hover:bg-purple-100 text-purple-600 transition-colors"
              >
                <ChevronRight className="h-5 w-5" />
              </button>
            </div>

            {/* Days of Week */}
            <div className="grid grid-cols-7 gap-1 mb-2">
              {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
                <div key={day} className="p-2 text-center text-sm font-medium text-gray-500">
                  {day}
                </div>
              ))}
            </div>

            {/* Calendar Grid */}
            <div className="grid grid-cols-7 gap-1">
              {monthDays.map(day => {
                const dayActivities = getActivitiesForDate(day);
                const isSelected = selectedDate && isSameDay(day, selectedDate);
                const isTodayDate = isToday(day);

                return (
                  <button
                    key={day.toISOString()}
                    onClick={() => handleDateClick(day)}
                    onDoubleClick={() => handleDateDoubleClick(day)}
                    className={`p-2 min-h-[80px] border border-gray-100 rounded-lg transition-all duration-200 hover:bg-purple-50 relative group ${
                      isSelected ? 'bg-purple-100 border-purple-300' : ''
                    } ${isTodayDate ? 'ring-2 ring-purple-300' : ''}`}
                    title="Click to select, double-click to add activity"
                  >
                    <div className="text-sm font-medium text-gray-800 mb-1">
                      {format(day, 'd')}
                    </div>

                    {/* Activity indicators */}
                    <div className="space-y-1">
                      {dayActivities.slice(0, 3).map((activity, index) => (
                        <div
                          key={activity.id}
                          className={`w-full h-1.5 rounded-full ${getCategoryColor(activity.category)} ${
                            activity.completed ? 'opacity-50' : ''
                          }`}
                          title={activity.title}
                        />
                      ))}
                      {dayActivities.length > 3 && (
                        <div className="text-xs text-gray-500">
                          +{dayActivities.length - 3} more
                        </div>
                      )}
                    </div>

                    {/* Quick add button on hover */}
                    <div className="absolute inset-0 bg-purple-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-200 rounded-lg flex items-center justify-center">
                      <Plus className="h-4 w-4 text-purple-600" />
                    </div>
                  </button>
                );
              })}
            </div>
          </div>
        </div>

        {/* Selected Date Activities */}
        <div className="lg:col-span-1">
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6">
            <h3 className="text-lg font-bold text-gray-800 mb-4">
              {selectedDate ? format(selectedDate, 'MMMM d, yyyy') : 'Select a date'}
            </h3>

            {selectedDate ? (
              <>
                <div className="flex items-center justify-between mb-4">
                  <span className="text-sm text-gray-500">
                    {selectedDateActivities.length} activities
                  </span>
                  <button
                    onClick={() => handleDateDoubleClick(selectedDate)}
                    className="flex items-center space-x-1 px-3 py-1 bg-purple-100 text-purple-700 text-sm rounded-lg hover:bg-purple-200 transition-colors"
                  >
                    <Plus className="h-3 w-3" />
                    <span>Add Activity</span>
                  </button>
                </div>

                {selectedDateActivities.length > 0 ? (
                  <div className="space-y-4">
                    {selectedDateActivities.map(activity => (
                      <div
                        key={activity.id}
                        className={`p-4 rounded-xl border-l-4 ${
                          activity.completed ? 'bg-gray-50 opacity-75' : 'bg-white'
                        }`}
                        style={{
                          borderLeftColor: getCategoryColor(activity.category).replace('bg-', '#'),
                        }}
                      >
                        <h4 className={`font-semibold text-gray-800 mb-1 ${
                          activity.completed ? 'line-through' : ''
                        }`}>
                          {activity.title}
                        </h4>
                        <p className="text-sm text-gray-600 mb-2">
                          {activity.description}
                        </p>
                        <div className="flex items-center justify-between text-xs">
                          <span className="text-gray-500">
                            ${activity.price.toFixed(2)}
                          </span>
                          <span className={`px-2 py-1 rounded-full text-white ${getCategoryColor(activity.category)}`}>
                            {activity.category}
                          </span>
                        </div>
                        {activity.completed && (
                          <div className="mt-2 text-xs text-green-600 font-medium">
                            ✓ Completed
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <CalendarIcon className="h-12 w-12 text-gray-300 mx-auto mb-3" />
                    <p className="text-gray-500 mb-3">No activities planned for this date</p>
                    <button
                      onClick={() => handleDateDoubleClick(selectedDate)}
                      className="inline-flex items-center space-x-2 px-4 py-2 bg-purple-500 text-white text-sm rounded-lg hover:bg-purple-600 transition-colors"
                    >
                      <Plus className="h-4 w-4" />
                      <span>Add Activity</span>
                    </button>
                  </div>
                )}
              </>
            ) : (
              <div className="text-center py-8">
                <Heart className="h-12 w-12 text-gray-300 mx-auto mb-3" />
                <p className="text-gray-500 mb-2">Click on a date to see planned activities</p>
                <p className="text-gray-400 text-sm">Double-click to add a new activity</p>
              </div>
            )}
          </div>

          {/* Legend */}
          <div className="mt-6 bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6">
            <h4 className="font-semibold text-gray-800 mb-3">Category Legend</h4>
            <div className="grid grid-cols-2 gap-2 text-xs">
              {[
                { category: 'dining', label: 'Dining' },
                { category: 'entertainment', label: 'Entertainment' },
                { category: 'outdoor', label: 'Outdoor' },
                { category: 'cultural', label: 'Cultural' },
                { category: 'romantic', label: 'Romantic' },
                { category: 'adventure', label: 'Adventure' },
                { category: 'relaxation', label: 'Relaxation' },
                { category: 'travel', label: 'Travel' },
                { category: 'sports', label: 'Sports' },
                { category: 'other', label: 'Other' },
              ].map(({ category, label }) => (
                <div key={category} className="flex items-center space-x-2">
                  <div className={`w-3 h-3 rounded-full ${getCategoryColor(category)}`} />
                  <span className="text-gray-600">{label}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Quick Add Activity Modal */}
      {showQuickAdd && quickAddDate && (
        <QuickActivityModal
          selectedDate={quickAddDate}
          onClose={() => {
            setShowQuickAdd(false);
            setQuickAddDate(null);
          }}
          onActivityCreated={handleActivityCreated}
        />
      )}
    </div>
  );
}
