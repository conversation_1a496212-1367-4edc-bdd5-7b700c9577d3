{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/activitiesdates/src/components/ActivityCard.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef } from 'react';\nimport { Activity } from '@/types';\nimport {\n  Calendar,\n  MapPin,\n  DollarSign,\n  Clock,\n  Star,\n  Edit,\n  Trash2,\n  Check,\n  X,\n  Heart,\n  Camera,\n  Image as ImageIcon\n} from 'lucide-react';\nimport { format } from 'date-fns';\nimport Image from 'next/image';\n\ninterface ActivityCardProps {\n  activity: Activity;\n  onUpdate: (activity: Activity) => void;\n  onDelete: (id: string) => void;\n}\n\nexport default function ActivityCard({ activity, onUpdate, onDelete }: ActivityCardProps) {\n  const [isEditing, setIsEditing] = useState(false);\n  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);\n  const [isUploading, setIsUploading] = useState(false);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  const handleComplete = async () => {\n    try {\n      const updatedActivity = {\n        ...activity,\n        completed: !activity.completed,\n        completedDate: !activity.completed ? new Date().toISOString() : undefined,\n      };\n\n      const response = await fetch('/api/activities', {\n        method: 'PUT',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(updatedActivity),\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        onUpdate(data.activity);\n      }\n    } catch (error) {\n      console.error('Error updating activity:', error);\n    }\n  };\n\n  const handleDelete = async () => {\n    try {\n      const response = await fetch(`/api/activities?id=${activity.id}`, {\n        method: 'DELETE',\n      });\n\n      if (response.ok) {\n        onDelete(activity.id);\n      }\n    } catch (error) {\n      console.error('Error deleting activity:', error);\n    }\n  };\n\n  const handleRating = async (rating: number) => {\n    try {\n      const updatedActivity = { ...activity, rating };\n\n      const response = await fetch('/api/activities', {\n        method: 'PUT',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(updatedActivity),\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        onUpdate(data.activity);\n      }\n    } catch (error) {\n      console.error('Error updating rating:', error);\n    }\n  };\n\n  const handlePhotoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0];\n    if (!file) return;\n\n    setIsUploading(true);\n    try {\n      const formData = new FormData();\n      formData.append('file', file);\n\n      const uploadResponse = await fetch('/api/upload', {\n        method: 'POST',\n        body: formData,\n      });\n\n      if (!uploadResponse.ok) {\n        const errorData = await uploadResponse.json();\n        throw new Error(errorData.error || 'Failed to upload photo');\n      }\n\n      const { filePath } = await uploadResponse.json();\n\n      // Update activity with new image\n      const updatedActivity = {\n        ...activity,\n        images: [...activity.images, filePath],\n      };\n\n      const response = await fetch('/api/activities', {\n        method: 'PUT',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(updatedActivity),\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        onUpdate(data.activity);\n      }\n    } catch (error) {\n      console.error('Error uploading photo:', error);\n      alert('Failed to upload photo. Please try again.');\n    } finally {\n      setIsUploading(false);\n      if (fileInputRef.current) {\n        fileInputRef.current.value = '';\n      }\n    }\n  };\n\n  const handleRemovePhoto = async (photoPath: string) => {\n    try {\n      const updatedActivity = {\n        ...activity,\n        images: activity.images.filter(img => img !== photoPath),\n      };\n\n      const response = await fetch('/api/activities', {\n        method: 'PUT',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(updatedActivity),\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        onUpdate(data.activity);\n      }\n    } catch (error) {\n      console.error('Error removing photo:', error);\n    }\n  };\n\n  const getCategoryColor = (category: string) => {\n    const colors = {\n      dining: 'from-orange-400 to-red-500',\n      entertainment: 'from-purple-400 to-pink-500',\n      outdoor: 'from-green-400 to-blue-500',\n      cultural: 'from-indigo-400 to-purple-500',\n      romantic: 'from-pink-400 to-rose-500',\n      adventure: 'from-yellow-400 to-orange-500',\n      relaxation: 'from-blue-400 to-indigo-500',\n      travel: 'from-teal-400 to-cyan-500',\n      sports: 'from-red-400 to-pink-500',\n      other: 'from-gray-400 to-gray-500',\n    };\n    return colors[category as keyof typeof colors] || colors.other;\n  };\n\n  return (\n    <div className={`bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-white/20 ${\n      activity.completed ? 'ring-2 ring-green-200' : ''\n    }`}>\n      {/* Header with category badge */}\n      <div className={`h-2 bg-gradient-to-r ${getCategoryColor(activity.category)}`} />\n      \n      <div className=\"p-6\">\n        {/* Title and Actions */}\n        <div className=\"flex items-start justify-between mb-4\">\n          <div className=\"flex-1\">\n            <h3 className={`text-xl font-bold text-gray-800 mb-2 ${activity.completed ? 'line-through text-gray-500' : ''}`}>\n              {activity.title}\n            </h3>\n            <p className=\"text-gray-600 text-sm leading-relaxed\">\n              {activity.description}\n            </p>\n          </div>\n          \n          <div className=\"flex items-center space-x-2 ml-4\">\n            <button\n              onClick={handleComplete}\n              className={`p-2 rounded-full transition-all duration-200 ${\n                activity.completed\n                  ? 'bg-green-100 text-green-600 hover:bg-green-200'\n                  : 'bg-gray-100 text-gray-400 hover:bg-green-100 hover:text-green-600'\n              }`}\n              title={activity.completed ? 'Mark as incomplete' : 'Mark as complete'}\n            >\n              <Check className=\"h-4 w-4\" />\n            </button>\n\n            {activity.completed && (\n              <button\n                onClick={() => fileInputRef.current?.click()}\n                disabled={isUploading}\n                className=\"p-2 rounded-full bg-gray-100 text-gray-400 hover:bg-blue-100 hover:text-blue-600 transition-all duration-200 disabled:opacity-50\"\n                title=\"Add photo\"\n              >\n                {isUploading ? (\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-2 border-gray-400 border-t-transparent\" />\n                ) : (\n                  <Camera className=\"h-4 w-4\" />\n                )}\n              </button>\n            )}\n\n            <button\n              onClick={() => setShowDeleteConfirm(true)}\n              className=\"p-2 rounded-full bg-gray-100 text-gray-400 hover:bg-red-100 hover:text-red-600 transition-all duration-200\"\n              title=\"Delete activity\"\n            >\n              <Trash2 className=\"h-4 w-4\" />\n            </button>\n          </div>\n\n          {/* Hidden file input */}\n          <input\n            ref={fileInputRef}\n            type=\"file\"\n            accept=\"image/*\"\n            onChange={handlePhotoUpload}\n            className=\"hidden\"\n          />\n        </div>\n\n        {/* Details */}\n        <div className=\"space-y-3 mb-4\">\n          {/* Price */}\n          <div className=\"flex items-center space-x-2 text-gray-600\">\n            <DollarSign className=\"h-4 w-4\" />\n            <span className=\"font-medium\">${activity.price.toFixed(2)}</span>\n          </div>\n\n          {/* Date */}\n          {activity.plannedDate && (\n            <div className=\"flex items-center space-x-2 text-gray-600\">\n              <Calendar className=\"h-4 w-4\" />\n              <span>{format(new Date(activity.plannedDate), 'MMM dd, yyyy')}</span>\n            </div>\n          )}\n\n          {/* Location */}\n          {activity.location && (\n            <div className=\"flex items-center space-x-2 text-gray-600\">\n              <MapPin className=\"h-4 w-4\" />\n              <span>{activity.location}</span>\n            </div>\n          )}\n\n          {/* Duration */}\n          {activity.duration && (\n            <div className=\"flex items-center space-x-2 text-gray-600\">\n              <Clock className=\"h-4 w-4\" />\n              <span>{activity.duration} minutes</span>\n            </div>\n          )}\n        </div>\n\n        {/* Tags */}\n        {activity.tags.length > 0 && (\n          <div className=\"flex flex-wrap gap-2 mb-4\">\n            {activity.tags.map((tag, index) => (\n              <span\n                key={index}\n                className=\"px-2 py-1 bg-purple-100 text-purple-700 text-xs rounded-full\"\n              >\n                {tag}\n              </span>\n            ))}\n          </div>\n        )}\n\n        {/* Photos (only show if completed) */}\n        {activity.completed && activity.images.length > 0 && (\n          <div className=\"mb-4\">\n            <h4 className=\"text-sm font-medium text-gray-700 mb-2\">Photos</h4>\n            <div className=\"grid grid-cols-2 gap-2\">\n              {activity.images.map((imagePath, index) => (\n                <div key={index} className=\"relative group\">\n                  <Image\n                    src={`/data/${imagePath}`}\n                    alt={`${activity.title} photo ${index + 1}`}\n                    width={150}\n                    height={100}\n                    className=\"w-full h-20 object-cover rounded-lg\"\n                  />\n                  <button\n                    onClick={() => handleRemovePhoto(imagePath)}\n                    className=\"absolute top-1 right-1 p-1 bg-red-500 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200\"\n                    title=\"Remove photo\"\n                  >\n                    <X className=\"h-3 w-3\" />\n                  </button>\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n\n        {/* Rating (only show if completed) */}\n        {activity.completed && (\n          <div className=\"flex items-center space-x-1 mb-4\">\n            <span className=\"text-sm text-gray-600 mr-2\">Rate this activity:</span>\n            {[1, 2, 3, 4, 5].map((star) => (\n              <button\n                key={star}\n                onClick={() => handleRating(star)}\n                className=\"transition-colors duration-200\"\n              >\n                <Star\n                  className={`h-5 w-5 ${\n                    star <= (activity.rating || 0)\n                      ? 'text-yellow-400 fill-current'\n                      : 'text-gray-300'\n                  }`}\n                />\n              </button>\n            ))}\n          </div>\n        )}\n\n        {/* Category Badge */}\n        <div className=\"flex justify-between items-center\">\n          <span className={`px-3 py-1 rounded-full text-xs font-medium text-white bg-gradient-to-r ${getCategoryColor(activity.category)}`}>\n            {activity.category.charAt(0).toUpperCase() + activity.category.slice(1)}\n          </span>\n          \n          {activity.completed && (\n            <div className=\"flex items-center space-x-1 text-green-600\">\n              <Check className=\"h-4 w-4\" />\n              <span className=\"text-xs font-medium\">Completed</span>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Delete Confirmation Modal */}\n      {showDeleteConfirm && (\n        <div className=\"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4\">\n          <div className=\"bg-white rounded-2xl p-6 max-w-sm w-full\">\n            <h3 className=\"text-lg font-bold text-gray-800 mb-2\">Delete Activity</h3>\n            <p className=\"text-gray-600 mb-6\">\n              Are you sure you want to delete \"{activity.title}\"? This action cannot be undone.\n            </p>\n            <div className=\"flex space-x-3\">\n              <button\n                onClick={() => setShowDeleteConfirm(false)}\n                className=\"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\"\n              >\n                Cancel\n              </button>\n              <button\n                onClick={() => {\n                  handleDelete();\n                  setShowDeleteConfirm(false);\n                }}\n                className=\"flex-1 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors\"\n              >\n                Delete\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AACA;;;AAnBA;;;;;AA2Be,SAAS,aAAa,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAqB;;IACtF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM,kBAAkB;gBACtB,GAAG,QAAQ;gBACX,WAAW,CAAC,SAAS,SAAS;gBAC9B,eAAe,CAAC,SAAS,SAAS,GAAG,IAAI,OAAO,WAAW,KAAK;YAClE;YAEA,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,SAAS,KAAK,QAAQ;YACxB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C;IACF;IAEA,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,mBAAmB,EAAE,SAAS,EAAE,EAAE,EAAE;gBAChE,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,SAAS,SAAS,EAAE;YACtB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,kBAAkB;gBAAE,GAAG,QAAQ;gBAAE;YAAO;YAE9C,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,SAAS,KAAK,QAAQ;YACxB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,CAAC,MAAM;QAEX,eAAe;QACf,IAAI;YACF,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ;YAExB,MAAM,iBAAiB,MAAM,MAAM,eAAe;gBAChD,QAAQ;gBACR,MAAM;YACR;YAEA,IAAI,CAAC,eAAe,EAAE,EAAE;gBACtB,MAAM,YAAY,MAAM,eAAe,IAAI;gBAC3C,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;YACrC;YAEA,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,eAAe,IAAI;YAE9C,iCAAiC;YACjC,MAAM,kBAAkB;gBACtB,GAAG,QAAQ;gBACX,QAAQ;uBAAI,SAAS,MAAM;oBAAE;iBAAS;YACxC;YAEA,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,SAAS,KAAK,QAAQ;YACxB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR,SAAU;YACR,eAAe;YACf,IAAI,aAAa,OAAO,EAAE;gBACxB,aAAa,OAAO,CAAC,KAAK,GAAG;YAC/B;QACF;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,IAAI;YACF,MAAM,kBAAkB;gBACtB,GAAG,QAAQ;gBACX,QAAQ,SAAS,MAAM,CAAC,MAAM,CAAC,CAAA,MAAO,QAAQ;YAChD;YAEA,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,SAAS,KAAK,QAAQ;YACxB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAS;YACb,QAAQ;YACR,eAAe;YACf,SAAS;YACT,UAAU;YACV,UAAU;YACV,WAAW;YACX,YAAY;YACZ,QAAQ;YACR,QAAQ;YACR,OAAO;QACT;QACA,OAAO,MAAM,CAAC,SAAgC,IAAI,OAAO,KAAK;IAChE;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,sIAAsI,EACrJ,SAAS,SAAS,GAAG,0BAA0B,IAC/C;;0BAEA,6LAAC;gBAAI,WAAW,CAAC,qBAAqB,EAAE,iBAAiB,SAAS,QAAQ,GAAG;;;;;;0BAE7E,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAW,CAAC,qCAAqC,EAAE,SAAS,SAAS,GAAG,+BAA+B,IAAI;kDAC5G,SAAS,KAAK;;;;;;kDAEjB,6LAAC;wCAAE,WAAU;kDACV,SAAS,WAAW;;;;;;;;;;;;0CAIzB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS;wCACT,WAAW,CAAC,6CAA6C,EACvD,SAAS,SAAS,GACd,mDACA,qEACJ;wCACF,OAAO,SAAS,SAAS,GAAG,uBAAuB;kDAEnD,cAAA,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;oCAGlB,SAAS,SAAS,kBACjB,6LAAC;wCACC,SAAS,IAAM,aAAa,OAAO,EAAE;wCACrC,UAAU;wCACV,WAAU;wCACV,OAAM;kDAEL,4BACC,6LAAC;4CAAI,WAAU;;;;;iEAEf,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;kDAKxB,6LAAC;wCACC,SAAS,IAAM,qBAAqB;wCACpC,WAAU;wCACV,OAAM;kDAEN,cAAA,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAKtB,6LAAC;gCACC,KAAK;gCACL,MAAK;gCACL,QAAO;gCACP,UAAU;gCACV,WAAU;;;;;;;;;;;;kCAKd,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,6LAAC;wCAAK,WAAU;;4CAAc;4CAAE,SAAS,KAAK,CAAC,OAAO,CAAC;;;;;;;;;;;;;4BAIxD,SAAS,WAAW,kBACnB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;kDAAM,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,SAAS,WAAW,GAAG;;;;;;;;;;;;4BAKjD,SAAS,QAAQ,kBAChB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;kDAAM,SAAS,QAAQ;;;;;;;;;;;;4BAK3B,SAAS,QAAQ,kBAChB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;;4CAAM,SAAS,QAAQ;4CAAC;;;;;;;;;;;;;;;;;;;oBAM9B,SAAS,IAAI,CAAC,MAAM,GAAG,mBACtB,6LAAC;wBAAI,WAAU;kCACZ,SAAS,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,sBACvB,6LAAC;gCAEC,WAAU;0CAET;+BAHI;;;;;;;;;;oBAUZ,SAAS,SAAS,IAAI,SAAS,MAAM,CAAC,MAAM,GAAG,mBAC9C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAI,WAAU;0CACZ,SAAS,MAAM,CAAC,GAAG,CAAC,CAAC,WAAW,sBAC/B,6LAAC;wCAAgB,WAAU;;0DACzB,6LAAC,gIAAA,CAAA,UAAK;gDACJ,KAAK,CAAC,MAAM,EAAE,WAAW;gDACzB,KAAK,GAAG,SAAS,KAAK,CAAC,OAAO,EAAE,QAAQ,GAAG;gDAC3C,OAAO;gDACP,QAAQ;gDACR,WAAU;;;;;;0DAEZ,6LAAC;gDACC,SAAS,IAAM,kBAAkB;gDACjC,WAAU;gDACV,OAAM;0DAEN,cAAA,6LAAC,+LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;;;;;;uCAbP;;;;;;;;;;;;;;;;oBAsBjB,SAAS,SAAS,kBACjB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAA6B;;;;;;4BAC5C;gCAAC;gCAAG;gCAAG;gCAAG;gCAAG;6BAAE,CAAC,GAAG,CAAC,CAAC,qBACpB,6LAAC;oCAEC,SAAS,IAAM,aAAa;oCAC5B,WAAU;8CAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;wCACH,WAAW,CAAC,QAAQ,EAClB,QAAQ,CAAC,SAAS,MAAM,IAAI,CAAC,IACzB,iCACA,iBACJ;;;;;;mCATC;;;;;;;;;;;kCAiBb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAW,CAAC,uEAAuE,EAAE,iBAAiB,SAAS,QAAQ,GAAG;0CAC7H,SAAS,QAAQ,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,SAAS,QAAQ,CAAC,KAAK,CAAC;;;;;;4BAGtE,SAAS,SAAS,kBACjB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;wCAAK,WAAU;kDAAsB;;;;;;;;;;;;;;;;;;;;;;;;YAO7C,mCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAuC;;;;;;sCACrD,6LAAC;4BAAE,WAAU;;gCAAqB;gCACE,SAAS,KAAK;gCAAC;;;;;;;sCAEnD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,qBAAqB;oCACpC,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS;wCACP;wCACA,qBAAqB;oCACvB;oCACA,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GAnWwB;KAAA", "debugId": null}}, {"offset": {"line": 648, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/activitiesdates/src/app/activities/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Activity } from '@/types';\nimport ActivityCard from '@/components/ActivityCard';\nimport { Search, Plus, Activity as ActivityIcon, Calendar, CheckCircle } from 'lucide-react';\nimport Link from 'next/link';\nimport { isAfter, startOfDay } from 'date-fns';\n\nexport default function Activities() {\n  const [activities, setActivities] = useState<Activity[]>([]);\n  const [upcomingActivities, setUpcomingActivities] = useState<Activity[]>([]);\n  const [completedActivities, setCompletedActivities] = useState<Activity[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [activeTab, setActiveTab] = useState<'upcoming' | 'completed'>('upcoming');\n\n  useEffect(() => {\n    fetchActivities();\n  }, []);\n\n  useEffect(() => {\n    // Separate activities into upcoming and completed\n    const today = startOfDay(new Date());\n    \n    const upcoming = activities.filter(activity => {\n      if (activity.completed) return false;\n      if (!activity.plannedDate) return true;\n      return isAfter(new Date(activity.plannedDate), today) || \n             startOfDay(new Date(activity.plannedDate)).getTime() === today.getTime();\n    });\n\n    const completed = activities.filter(activity => activity.completed);\n\n    // Apply search filter\n    const filterBySearch = (activityList: Activity[]) => {\n      if (!searchTerm) return activityList;\n      return activityList.filter(activity =>\n        activity.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        activity.description.toLowerCase().includes(searchTerm.toLowerCase()) ||\n        activity.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))\n      );\n    };\n\n    // Sort upcoming by planned date (soonest first)\n    upcoming.sort((a, b) => {\n      if (a.plannedDate && b.plannedDate) {\n        return new Date(a.plannedDate).getTime() - new Date(b.plannedDate).getTime();\n      }\n      if (a.plannedDate && !b.plannedDate) return -1;\n      if (!a.plannedDate && b.plannedDate) return 1;\n      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();\n    });\n\n    // Sort completed by completion date (most recent first)\n    completed.sort((a, b) => {\n      if (a.completedDate && b.completedDate) {\n        return new Date(b.completedDate).getTime() - new Date(a.completedDate).getTime();\n      }\n      return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();\n    });\n\n    setUpcomingActivities(filterBySearch(upcoming));\n    setCompletedActivities(filterBySearch(completed));\n  }, [activities, searchTerm]);\n\n  const fetchActivities = async () => {\n    try {\n      const response = await fetch('/api/activities');\n      const data = await response.json();\n      setActivities(data.activities || []);\n    } catch (error) {\n      console.error('Error fetching activities:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleActivityUpdate = (updatedActivity: Activity) => {\n    setActivities(prev => \n      prev.map(activity => \n        activity.id === updatedActivity.id ? updatedActivity : activity\n      )\n    );\n  };\n\n  const handleActivityDelete = (deletedId: string) => {\n    setActivities(prev => prev.filter(activity => activity.id !== deletedId));\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-[400px]\">\n        <div className=\"animate-pulse-slow\">\n          <ActivityIcon className=\"h-12 w-12 text-purple-500\" />\n        </div>\n      </div>\n    );\n  }\n\n  const currentActivities = activeTab === 'upcoming' ? upcomingActivities : completedActivities;\n\n  return (\n    <div className=\"space-y-8\">\n      {/* Header */}\n      <div className=\"text-center space-y-4\">\n        <h1 className=\"text-4xl md:text-6xl font-bold bg-gradient-to-r from-purple-600 via-pink-600 to-orange-500 bg-clip-text text-transparent\">\n          Activities\n        </h1>\n        <p className=\"text-gray-600 text-lg max-w-2xl mx-auto\">\n          Manage your planned and completed activities\n        </p>\n      </div>\n\n      {/* Stats */}\n      <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n        <div className=\"bg-white/80 backdrop-blur-sm rounded-xl p-4 text-center border border-white/20\">\n          <div className=\"text-2xl font-bold text-gray-800\">{activities.length}</div>\n          <div className=\"text-sm text-gray-600\">Total Activities</div>\n        </div>\n        <div className=\"bg-blue-50/80 backdrop-blur-sm rounded-xl p-4 text-center border border-blue-200/20\">\n          <div className=\"text-2xl font-bold text-blue-600\">{upcomingActivities.length}</div>\n          <div className=\"text-sm text-blue-600\">Upcoming</div>\n        </div>\n        <div className=\"bg-green-50/80 backdrop-blur-sm rounded-xl p-4 text-center border border-green-200/20\">\n          <div className=\"text-2xl font-bold text-green-600\">{completedActivities.length}</div>\n          <div className=\"text-sm text-green-600\">Completed</div>\n        </div>\n        <div className=\"bg-purple-50/80 backdrop-blur-sm rounded-xl p-4 text-center border border-purple-200/20\">\n          <div className=\"text-2xl font-bold text-purple-600\">\n            ${activities.reduce((sum, a) => sum + a.price, 0).toFixed(0)}\n          </div>\n          <div className=\"text-sm text-purple-600\">Total Spent</div>\n        </div>\n      </div>\n\n      {/* Search and Add Button */}\n      <div className=\"flex flex-col sm:flex-row gap-4 items-center justify-between\">\n        <div className=\"relative flex-1 max-w-md\">\n          <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5\" />\n          <input\n            type=\"text\"\n            placeholder=\"Search activities...\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            className=\"w-full pl-10 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 bg-white/80 backdrop-blur-sm\"\n          />\n        </div>\n        \n        <Link\n          href=\"/add\"\n          className=\"flex items-center space-x-2 bg-gradient-to-r from-purple-500 to-pink-500 text-white px-6 py-3 rounded-xl hover:from-purple-600 hover:to-pink-600 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1\"\n        >\n          <Plus className=\"h-5 w-5\" />\n          <span className=\"font-medium\">Add Activity</span>\n        </Link>\n      </div>\n\n      {/* Tabs */}\n      <div className=\"flex space-x-1 bg-white/80 backdrop-blur-sm rounded-xl p-1 border border-white/20\">\n        <button\n          onClick={() => setActiveTab('upcoming')}\n          className={`flex-1 flex items-center justify-center space-x-2 px-4 py-3 rounded-lg transition-all duration-200 ${\n            activeTab === 'upcoming'\n              ? 'bg-gradient-to-r from-blue-500 to-indigo-500 text-white shadow-lg'\n              : 'text-gray-600 hover:text-blue-600 hover:bg-blue-50'\n          }`}\n        >\n          <Calendar className=\"h-4 w-4\" />\n          <span className=\"font-medium\">Upcoming ({upcomingActivities.length})</span>\n        </button>\n        <button\n          onClick={() => setActiveTab('completed')}\n          className={`flex-1 flex items-center justify-center space-x-2 px-4 py-3 rounded-lg transition-all duration-200 ${\n            activeTab === 'completed'\n              ? 'bg-gradient-to-r from-green-500 to-emerald-500 text-white shadow-lg'\n              : 'text-gray-600 hover:text-green-600 hover:bg-green-50'\n          }`}\n        >\n          <CheckCircle className=\"h-4 w-4\" />\n          <span className=\"font-medium\">Completed ({completedActivities.length})</span>\n        </button>\n      </div>\n\n      {/* Activities Grid */}\n      {currentActivities.length === 0 ? (\n        <div className=\"text-center py-16\">\n          {activeTab === 'upcoming' ? (\n            <>\n              <Calendar className=\"h-16 w-16 text-gray-300 mx-auto mb-4\" />\n              <h3 className=\"text-xl font-semibold text-gray-600 mb-2\">\n                {activities.length === 0 ? 'No activities yet' : 'No upcoming activities'}\n              </h3>\n              <p className=\"text-gray-500 mb-6\">\n                {activities.length === 0 \n                  ? 'Start planning your perfect dates and activities!'\n                  : searchTerm \n                    ? 'Try adjusting your search terms'\n                    : 'All your activities are completed!'\n                }\n              </p>\n              {activities.length === 0 && (\n                <Link\n                  href=\"/add\"\n                  className=\"inline-flex items-center space-x-2 bg-gradient-to-r from-purple-500 to-pink-500 text-white px-6 py-3 rounded-xl hover:from-purple-600 hover:to-pink-600 transition-all duration-200\"\n                >\n                  <Plus className=\"h-5 w-5\" />\n                  <span>Add Your First Activity</span>\n                </Link>\n              )}\n            </>\n          ) : (\n            <>\n              <CheckCircle className=\"h-16 w-16 text-gray-300 mx-auto mb-4\" />\n              <h3 className=\"text-xl font-semibold text-gray-600 mb-2\">\n                No completed activities\n              </h3>\n              <p className=\"text-gray-500 mb-6\">\n                {searchTerm \n                  ? 'Try adjusting your search terms'\n                  : 'Complete some activities to see them here!'\n                }\n              </p>\n            </>\n          )}\n        </div>\n      ) : (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {currentActivities.map((activity, index) => (\n            <div key={activity.id} className=\"animate-fade-in-up\" style={{ animationDelay: `${index * 0.1}s` }}>\n              <ActivityCard\n                activity={activity}\n                onUpdate={handleActivityUpdate}\n                onDelete={handleActivityDelete}\n              />\n            </div>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;;;AAPA;;;;;;AASe,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC3D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC3E,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC7E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4B;IAErE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR;QACF;+BAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,kDAAkD;YAClD,MAAM,QAAQ,CAAA,GAAA,4IAAA,CAAA,aAAU,AAAD,EAAE,IAAI;YAE7B,MAAM,WAAW,WAAW,MAAM;iDAAC,CAAA;oBACjC,IAAI,SAAS,SAAS,EAAE,OAAO;oBAC/B,IAAI,CAAC,SAAS,WAAW,EAAE,OAAO;oBAClC,OAAO,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,IAAI,KAAK,SAAS,WAAW,GAAG,UACxC,CAAA,GAAA,4IAAA,CAAA,aAAU,AAAD,EAAE,IAAI,KAAK,SAAS,WAAW,GAAG,OAAO,OAAO,MAAM,OAAO;gBAC/E;;YAEA,MAAM,YAAY,WAAW,MAAM;kDAAC,CAAA,WAAY,SAAS,SAAS;;YAElE,sBAAsB;YACtB,MAAM;uDAAiB,CAAC;oBACtB,IAAI,CAAC,YAAY,OAAO;oBACxB,OAAO,aAAa,MAAM;+DAAC,CAAA,WACzB,SAAS,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC5D,SAAS,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAClE,SAAS,IAAI,CAAC,IAAI;uEAAC,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;;;gBAE/E;;YAEA,gDAAgD;YAChD,SAAS,IAAI;wCAAC,CAAC,GAAG;oBAChB,IAAI,EAAE,WAAW,IAAI,EAAE,WAAW,EAAE;wBAClC,OAAO,IAAI,KAAK,EAAE,WAAW,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,WAAW,EAAE,OAAO;oBAC5E;oBACA,IAAI,EAAE,WAAW,IAAI,CAAC,EAAE,WAAW,EAAE,OAAO,CAAC;oBAC7C,IAAI,CAAC,EAAE,WAAW,IAAI,EAAE,WAAW,EAAE,OAAO;oBAC5C,OAAO,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;gBACxE;;YAEA,wDAAwD;YACxD,UAAU,IAAI;wCAAC,CAAC,GAAG;oBACjB,IAAI,EAAE,aAAa,IAAI,EAAE,aAAa,EAAE;wBACtC,OAAO,IAAI,KAAK,EAAE,aAAa,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,aAAa,EAAE,OAAO;oBAChF;oBACA,OAAO,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;gBACxE;;YAEA,sBAAsB,eAAe;YACrC,uBAAuB,eAAe;QACxC;+BAAG;QAAC;QAAY;KAAW;IAE3B,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,cAAc,KAAK,UAAU,IAAI,EAAE;QACrC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,cAAc,CAAA,OACZ,KAAK,GAAG,CAAC,CAAA,WACP,SAAS,EAAE,KAAK,gBAAgB,EAAE,GAAG,kBAAkB;IAG7D;IAEA,MAAM,uBAAuB,CAAC;QAC5B,cAAc,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,WAAY,SAAS,EAAE,KAAK;IAChE;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6MAAA,CAAA,WAAY;oBAAC,WAAU;;;;;;;;;;;;;;;;IAIhC;IAEA,MAAM,oBAAoB,cAAc,aAAa,qBAAqB;IAE1E,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2H;;;;;;kCAGzI,6LAAC;wBAAE,WAAU;kCAA0C;;;;;;;;;;;;0BAMzD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAoC,WAAW,MAAM;;;;;;0CACpE,6LAAC;gCAAI,WAAU;0CAAwB;;;;;;;;;;;;kCAEzC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAoC,mBAAmB,MAAM;;;;;;0CAC5E,6LAAC;gCAAI,WAAU;0CAAwB;;;;;;;;;;;;kCAEzC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAqC,oBAAoB,MAAM;;;;;;0CAC9E,6LAAC;gCAAI,WAAU;0CAAyB;;;;;;;;;;;;kCAE1C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;oCAAqC;oCAChD,WAAW,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;;;;;;;0CAE5D,6LAAC;gCAAI,WAAU;0CAA0B;;;;;;;;;;;;;;;;;;0BAK7C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,WAAU;;;;;;;;;;;;kCAId,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;;0CAEV,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,6LAAC;gCAAK,WAAU;0CAAc;;;;;;;;;;;;;;;;;;0BAKlC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS,IAAM,aAAa;wBAC5B,WAAW,CAAC,mGAAmG,EAC7G,cAAc,aACV,sEACA,sDACJ;;0CAEF,6LAAC,6MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,6LAAC;gCAAK,WAAU;;oCAAc;oCAAW,mBAAmB,MAAM;oCAAC;;;;;;;;;;;;;kCAErE,6LAAC;wBACC,SAAS,IAAM,aAAa;wBAC5B,WAAW,CAAC,mGAAmG,EAC7G,cAAc,cACV,wEACA,wDACJ;;0CAEF,6LAAC,8NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,6LAAC;gCAAK,WAAU;;oCAAc;oCAAY,oBAAoB,MAAM;oCAAC;;;;;;;;;;;;;;;;;;;YAKxE,kBAAkB,MAAM,KAAK,kBAC5B,6LAAC;gBAAI,WAAU;0BACZ,cAAc,2BACb;;sCACE,6LAAC,6MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,6LAAC;4BAAG,WAAU;sCACX,WAAW,MAAM,KAAK,IAAI,sBAAsB;;;;;;sCAEnD,6LAAC;4BAAE,WAAU;sCACV,WAAW,MAAM,KAAK,IACnB,sDACA,aACE,oCACA;;;;;;wBAGP,WAAW,MAAM,KAAK,mBACrB,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,6LAAC;8CAAK;;;;;;;;;;;;;iDAKZ;;sCACE,6LAAC,8NAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,6LAAC;4BAAG,WAAU;sCAA2C;;;;;;sCAGzD,6LAAC;4BAAE,WAAU;sCACV,aACG,oCACA;;;;;;;;;;;;qCAOZ,6LAAC;gBAAI,WAAU;0BACZ,kBAAkB,GAAG,CAAC,CAAC,UAAU,sBAChC,6LAAC;wBAAsB,WAAU;wBAAqB,OAAO;4BAAE,gBAAgB,GAAG,QAAQ,IAAI,CAAC,CAAC;wBAAC;kCAC/F,cAAA,6LAAC,qIAAA,CAAA,UAAY;4BACX,UAAU;4BACV,UAAU;4BACV,UAAU;;;;;;uBAJJ,SAAS,EAAE;;;;;;;;;;;;;;;;AAYjC;GAxOwB;KAAA", "debugId": null}}]}