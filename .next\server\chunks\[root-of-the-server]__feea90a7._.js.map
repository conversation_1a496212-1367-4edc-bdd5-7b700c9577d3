{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/activitiesdates/src/app/api/data/%5B...path%5D/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { promises as fs } from 'fs';\nimport path from 'path';\n\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: { path: string[] } }\n) {\n  try {\n    const filePath = path.join(process.cwd(), 'data', ...params.path);\n    \n    // Security check - ensure the path is within the data directory\n    const dataDir = path.join(process.cwd(), 'data');\n    const resolvedPath = path.resolve(filePath);\n    const resolvedDataDir = path.resolve(dataDir);\n    \n    if (!resolvedPath.startsWith(resolvedDataDir)) {\n      return NextResponse.json({ error: 'Access denied' }, { status: 403 });\n    }\n\n    // Check if file exists\n    try {\n      await fs.access(filePath);\n    } catch {\n      return NextResponse.json({ error: 'File not found' }, { status: 404 });\n    }\n\n    // Read the file\n    const fileBuffer = await fs.readFile(filePath);\n    \n    // Determine content type based on file extension\n    const ext = path.extname(filePath).toLowerCase();\n    const contentTypes: { [key: string]: string } = {\n      '.jpg': 'image/jpeg',\n      '.jpeg': 'image/jpeg',\n      '.png': 'image/png',\n      '.gif': 'image/gif',\n      '.webp': 'image/webp',\n    };\n    \n    const contentType = contentTypes[ext] || 'application/octet-stream';\n\n    return new NextResponse(fileBuffer, {\n      headers: {\n        'Content-Type': contentType,\n        'Cache-Control': 'public, max-age=31536000', // Cache for 1 year\n      },\n    });\n  } catch (error) {\n    console.error('Error serving file:', error);\n    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAAkC;IAE1C,IAAI;QACF,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,WAAW,OAAO,IAAI;QAEhE,gEAAgE;QAChE,MAAM,UAAU,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI;QACzC,MAAM,eAAe,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC;QAClC,MAAM,kBAAkB,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC;QAErC,IAAI,CAAC,aAAa,UAAU,CAAC,kBAAkB;YAC7C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAgB,GAAG;gBAAE,QAAQ;YAAI;QACrE;QAEA,uBAAuB;QACvB,IAAI;YACF,MAAM,6FAAA,CAAA,WAAE,CAAC,MAAM,CAAC;QAClB,EAAE,OAAM;YACN,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAiB,GAAG;gBAAE,QAAQ;YAAI;QACtE;QAEA,gBAAgB;QAChB,MAAM,aAAa,MAAM,6FAAA,CAAA,WAAE,CAAC,QAAQ,CAAC;QAErC,iDAAiD;QACjD,MAAM,MAAM,iGAAA,CAAA,UAAI,CAAC,OAAO,CAAC,UAAU,WAAW;QAC9C,MAAM,eAA0C;YAC9C,QAAQ;YACR,SAAS;YACT,QAAQ;YACR,QAAQ;YACR,SAAS;QACX;QAEA,MAAM,cAAc,YAAY,CAAC,IAAI,IAAI;QAEzC,OAAO,IAAI,gIAAA,CAAA,eAAY,CAAC,YAAY;YAClC,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB;YACnB;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAwB,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF", "debugId": null}}]}