'use client';

import { useState, useEffect } from 'react';
import { Idea } from '@/types';
import IdeaCard from '@/components/IdeaCard';
import { Search, Plus, Lightbulb, Filter } from 'lucide-react';
import Link from 'next/link';

// Prevent hydration issues
function useIsClient() {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  return isClient;
}

export default function Ideas() {
  const [ideas, setIdeas] = useState<Idea[]>([]);
  const [filteredIdeas, setFilteredIdeas] = useState<Idea[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<'all' | 'not-proposed' | 'proposed' | 'accepted' | 'rejected'>('all');
  const isClient = useIsClient();

  useEffect(() => {
    if (isClient) {
      fetchIdeas();
    }
  }, [isClient]);

  useEffect(() => {
    let filtered = ideas;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(idea =>
        idea.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        idea.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        idea.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Filter by status
    switch (filterStatus) {
      case 'not-proposed':
        filtered = filtered.filter(idea => !idea.proposed);
        break;
      case 'proposed':
        filtered = filtered.filter(idea => idea.proposed && idea.accepted === undefined);
        break;
      case 'accepted':
        filtered = filtered.filter(idea => idea.accepted === true);
        break;
      case 'rejected':
        filtered = filtered.filter(idea => idea.accepted === false);
        break;
    }

    setFilteredIdeas(filtered);
  }, [ideas, searchTerm, filterStatus]);

  const fetchIdeas = async () => {
    try {
      const response = await fetch('/api/ideas');
      const data = await response.json();
      setIdeas(data.ideas || []);
    } catch (error) {
      console.error('Error fetching ideas:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleIdeaUpdate = (updatedIdea: Idea) => {
    setIdeas(prev => 
      prev.map(idea => 
        idea.id === updatedIdea.id ? updatedIdea : idea
      )
    );
  };

  const handleIdeaDelete = (deletedId: string) => {
    setIdeas(prev => prev.filter(idea => idea.id !== deletedId));
  };

  const getStatusCounts = () => {
    return {
      total: ideas.length,
      notProposed: ideas.filter(i => !i.proposed).length,
      proposed: ideas.filter(i => i.proposed && i.accepted === undefined).length,
      accepted: ideas.filter(i => i.accepted === true).length,
      rejected: ideas.filter(i => i.accepted === false).length,
    };
  };

  const statusCounts = getStatusCounts();

  if (!isClient || loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-pulse-slow">
          <Lightbulb className="h-12 w-12 text-yellow-500" />
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-yellow-500 via-orange-500 to-red-500 bg-clip-text text-transparent">
          Date Ideas
        </h1>
        <p className="text-gray-600 text-lg max-w-2xl mx-auto">
          Brainstorm and propose perfect date ideas to your girlfriend
        </p>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        <div className="bg-white/80 backdrop-blur-sm rounded-xl p-4 text-center border border-white/20">
          <div className="text-2xl font-bold text-gray-800">{statusCounts.total}</div>
          <div className="text-sm text-gray-600">Total Ideas</div>
        </div>
        <div className="bg-blue-50/80 backdrop-blur-sm rounded-xl p-4 text-center border border-blue-200/20">
          <div className="text-2xl font-bold text-blue-600">{statusCounts.notProposed}</div>
          <div className="text-sm text-blue-600">Not Proposed</div>
        </div>
        <div className="bg-yellow-50/80 backdrop-blur-sm rounded-xl p-4 text-center border border-yellow-200/20">
          <div className="text-2xl font-bold text-yellow-600">{statusCounts.proposed}</div>
          <div className="text-sm text-yellow-600">Waiting Response</div>
        </div>
        <div className="bg-green-50/80 backdrop-blur-sm rounded-xl p-4 text-center border border-green-200/20">
          <div className="text-2xl font-bold text-green-600">{statusCounts.accepted}</div>
          <div className="text-sm text-green-600">Accepted</div>
        </div>
        <div className="bg-red-50/80 backdrop-blur-sm rounded-xl p-4 text-center border border-red-200/20">
          <div className="text-2xl font-bold text-red-600">{statusCounts.rejected}</div>
          <div className="text-sm text-red-600">Rejected</div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
        <div className="flex flex-col sm:flex-row gap-4 flex-1">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <input
              type="text"
              placeholder="Search ideas..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-200 bg-white/80 backdrop-blur-sm"
            />
          </div>
          
          <div className="relative">
            <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value as any)}
              className="pl-10 pr-8 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-200 bg-white/80 backdrop-blur-sm appearance-none"
            >
              <option value="all">All Ideas</option>
              <option value="not-proposed">Not Proposed</option>
              <option value="proposed">Waiting Response</option>
              <option value="accepted">Accepted</option>
              <option value="rejected">Rejected</option>
            </select>
          </div>
        </div>
        
        <Link
          href="/ideas/add"
          className="flex items-center space-x-2 bg-gradient-to-r from-yellow-500 to-orange-500 text-white px-6 py-3 rounded-xl hover:from-yellow-600 hover:to-orange-600 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
        >
          <Plus className="h-5 w-5" />
          <span className="font-medium">Add Idea</span>
        </Link>
      </div>

      {/* Ideas Grid */}
      {filteredIdeas.length === 0 ? (
        <div className="text-center py-16">
          <Lightbulb className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-600 mb-2">
            {ideas.length === 0 ? 'No ideas yet' : 'No ideas found'}
          </h3>
          <p className="text-gray-500 mb-6">
            {ideas.length === 0 
              ? 'Start brainstorming your perfect date ideas!'
              : 'Try adjusting your search or filter'
            }
          </p>
          {ideas.length === 0 && (
            <Link
              href="/ideas/add"
              className="inline-flex items-center space-x-2 bg-gradient-to-r from-yellow-500 to-orange-500 text-white px-6 py-3 rounded-xl hover:from-yellow-600 hover:to-orange-600 transition-all duration-200"
            >
              <Plus className="h-5 w-5" />
              <span>Add Your First Idea</span>
            </Link>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredIdeas.map((idea, index) => (
            <div key={idea.id} className="animate-fade-in-up" style={{ animationDelay: `${index * 0.1}s` }}>
              <IdeaCard
                idea={idea}
                onUpdate={handleIdeaUpdate}
                onDelete={handleIdeaDelete}
              />
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
