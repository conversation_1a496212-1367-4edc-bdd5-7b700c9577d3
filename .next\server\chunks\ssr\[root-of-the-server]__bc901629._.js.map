{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/activitiesdates/src/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { Heart, Plus, Calendar, Home } from 'lucide-react';\n\nexport default function Navigation() {\n  const pathname = usePathname();\n\n  const navItems = [\n    { href: '/', label: 'Home', icon: Home },\n    { href: '/add', label: 'Add Activity', icon: Plus },\n    { href: '/calendar', label: 'Calendar', icon: Calendar },\n  ];\n\n  return (\n    <nav className=\"bg-white/80 backdrop-blur-md border-b border-purple-100 sticky top-0 z-50\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2 text-purple-600 hover:text-purple-700 transition-colors\">\n            <Heart className=\"h-8 w-8 fill-current\" />\n            <span className=\"text-xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent\">\n              Date Planner\n            </span>\n          </Link>\n\n          {/* Navigation Links */}\n          <div className=\"flex items-center space-x-1\">\n            {navItems.map((item) => {\n              const Icon = item.icon;\n              const isActive = pathname === item.href;\n              \n              return (\n                <Link\n                  key={item.href}\n                  href={item.href}\n                  className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-all duration-200 ${\n                    isActive\n                      ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg'\n                      : 'text-gray-600 hover:text-purple-600 hover:bg-purple-50'\n                  }`}\n                >\n                  <Icon className=\"h-4 w-4\" />\n                  <span className=\"font-medium\">{item.label}</span>\n                </Link>\n              );\n            })}\n          </div>\n        </div>\n      </div>\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,WAAW;QACf;YAAE,MAAM;YAAK,OAAO;YAAQ,MAAM,mMAAA,CAAA,OAAI;QAAC;QACvC;YAAE,MAAM;YAAQ,OAAO;YAAgB,MAAM,kMAAA,CAAA,OAAI;QAAC;QAClD;YAAE,MAAM;YAAa,OAAO;YAAY,MAAM,0MAAA,CAAA,WAAQ;QAAC;KACxD;IAED,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAI,WAAU;;0CACvB,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;gCAAK,WAAU;0CAA+F;;;;;;;;;;;;kCAMjH,8OAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC;4BACb,MAAM,OAAO,KAAK,IAAI;4BACtB,MAAM,WAAW,aAAa,KAAK,IAAI;4BAEvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAC,6EAA6E,EACvF,WACI,sEACA,0DACJ;;kDAEF,8OAAC;wCAAK,WAAU;;;;;;kDAChB,8OAAC;wCAAK,WAAU;kDAAe,KAAK,KAAK;;;;;;;+BATpC,KAAK,IAAI;;;;;wBAYpB;;;;;;;;;;;;;;;;;;;;;;AAMZ", "debugId": null}}]}