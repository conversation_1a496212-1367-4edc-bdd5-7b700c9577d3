{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/activitiesdates/src/lib/storage.ts"], "sourcesContent": ["import { promises as fs } from 'fs';\nimport path from 'path';\nimport { Activity } from '@/types';\n\nconst DATA_DIR = path.join(process.cwd(), 'data');\nconst ACTIVITIES_FILE = path.join(DATA_DIR, 'activities.json');\nconst UPLOADS_DIR = path.join(DATA_DIR, 'uploads');\n\n// Ensure data directory exists\nexport async function ensureDataDir() {\n  try {\n    await fs.access(DATA_DIR);\n  } catch {\n    await fs.mkdir(DATA_DIR, { recursive: true });\n  }\n  \n  try {\n    await fs.access(UPLOADS_DIR);\n  } catch {\n    await fs.mkdir(UPLOADS_DIR, { recursive: true });\n  }\n}\n\n// Read activities from JSON file\nexport async function readActivities(): Promise<Activity[]> {\n  try {\n    await ensureDataDir();\n    const data = await fs.readFile(ACTIVITIES_FILE, 'utf-8');\n    const parsed = JSON.parse(data);\n    return parsed.activities || [];\n  } catch (error) {\n    // If file doesn't exist or is invalid, return empty array\n    return [];\n  }\n}\n\n// Write activities to JSON file\nexport async function writeActivities(activities: Activity[]): Promise<void> {\n  await ensureDataDir();\n  const data = {\n    activities,\n    lastUpdated: new Date().toISOString(),\n  };\n  await fs.writeFile(ACTIVITIES_FILE, JSON.stringify(data, null, 2));\n}\n\n// Add a new activity\nexport async function addActivity(activity: Omit<Activity, 'id' | 'createdAt' | 'updatedAt'>): Promise<Activity> {\n  const activities = await readActivities();\n  const newActivity: Activity = {\n    ...activity,\n    id: generateId(),\n    createdAt: new Date().toISOString(),\n    updatedAt: new Date().toISOString(),\n  };\n  \n  activities.push(newActivity);\n  await writeActivities(activities);\n  return newActivity;\n}\n\n// Update an existing activity\nexport async function updateActivity(id: string, updates: Partial<Activity>): Promise<Activity | null> {\n  const activities = await readActivities();\n  const index = activities.findIndex(a => a.id === id);\n  \n  if (index === -1) return null;\n  \n  activities[index] = {\n    ...activities[index],\n    ...updates,\n    updatedAt: new Date().toISOString(),\n  };\n  \n  await writeActivities(activities);\n  return activities[index];\n}\n\n// Delete an activity\nexport async function deleteActivity(id: string): Promise<boolean> {\n  const activities = await readActivities();\n  const filteredActivities = activities.filter(a => a.id !== id);\n  \n  if (filteredActivities.length === activities.length) {\n    return false; // Activity not found\n  }\n  \n  await writeActivities(filteredActivities);\n  return true;\n}\n\n// Simple ID generator\nfunction generateId(): string {\n  return Date.now().toString(36) + Math.random().toString(36).substr(2);\n}\n\n// Save uploaded file\nexport async function saveUploadedFile(file: File): Promise<string> {\n  await ensureDataDir();\n  \n  const fileName = `${Date.now()}-${file.name}`;\n  const filePath = path.join(UPLOADS_DIR, fileName);\n  \n  const buffer = Buffer.from(await file.arrayBuffer());\n  await fs.writeFile(filePath, buffer);\n  \n  return `uploads/${fileName}`;\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;AAGA,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI;AAC1C,MAAM,kBAAkB,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,UAAU;AAC5C,MAAM,cAAc,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,UAAU;AAGjC,eAAe;IACpB,IAAI;QACF,MAAM,6FAAA,CAAA,WAAE,CAAC,MAAM,CAAC;IAClB,EAAE,OAAM;QACN,MAAM,6FAAA,CAAA,WAAE,CAAC,KAAK,CAAC,UAAU;YAAE,WAAW;QAAK;IAC7C;IAEA,IAAI;QACF,MAAM,6FAAA,CAAA,WAAE,CAAC,MAAM,CAAC;IAClB,EAAE,OAAM;QACN,MAAM,6FAAA,CAAA,WAAE,CAAC,KAAK,CAAC,aAAa;YAAE,WAAW;QAAK;IAChD;AACF;AAGO,eAAe;IACpB,IAAI;QACF,MAAM;QACN,MAAM,OAAO,MAAM,6FAAA,CAAA,WAAE,CAAC,QAAQ,CAAC,iBAAiB;QAChD,MAAM,SAAS,KAAK,KAAK,CAAC;QAC1B,OAAO,OAAO,UAAU,IAAI,EAAE;IAChC,EAAE,OAAO,OAAO;QACd,0DAA0D;QAC1D,OAAO,EAAE;IACX;AACF;AAGO,eAAe,gBAAgB,UAAsB;IAC1D,MAAM;IACN,MAAM,OAAO;QACX;QACA,aAAa,IAAI,OAAO,WAAW;IACrC;IACA,MAAM,6FAAA,CAAA,WAAE,CAAC,SAAS,CAAC,iBAAiB,KAAK,SAAS,CAAC,MAAM,MAAM;AACjE;AAGO,eAAe,YAAY,QAA0D;IAC1F,MAAM,aAAa,MAAM;IACzB,MAAM,cAAwB;QAC5B,GAAG,QAAQ;QACX,IAAI;QACJ,WAAW,IAAI,OAAO,WAAW;QACjC,WAAW,IAAI,OAAO,WAAW;IACnC;IAEA,WAAW,IAAI,CAAC;IAChB,MAAM,gBAAgB;IACtB,OAAO;AACT;AAGO,eAAe,eAAe,EAAU,EAAE,OAA0B;IACzE,MAAM,aAAa,MAAM;IACzB,MAAM,QAAQ,WAAW,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAEjD,IAAI,UAAU,CAAC,GAAG,OAAO;IAEzB,UAAU,CAAC,MAAM,GAAG;QAClB,GAAG,UAAU,CAAC,MAAM;QACpB,GAAG,OAAO;QACV,WAAW,IAAI,OAAO,WAAW;IACnC;IAEA,MAAM,gBAAgB;IACtB,OAAO,UAAU,CAAC,MAAM;AAC1B;AAGO,eAAe,eAAe,EAAU;IAC7C,MAAM,aAAa,MAAM;IACzB,MAAM,qBAAqB,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAE3D,IAAI,mBAAmB,MAAM,KAAK,WAAW,MAAM,EAAE;QACnD,OAAO,OAAO,qBAAqB;IACrC;IAEA,MAAM,gBAAgB;IACtB,OAAO;AACT;AAEA,sBAAsB;AACtB,SAAS;IACP,OAAO,KAAK,GAAG,GAAG,QAAQ,CAAC,MAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC;AACrE;AAGO,eAAe,iBAAiB,IAAU;IAC/C,MAAM;IAEN,MAAM,WAAW,GAAG,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,IAAI,EAAE;IAC7C,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,aAAa;IAExC,MAAM,SAAS,OAAO,IAAI,CAAC,MAAM,KAAK,WAAW;IACjD,MAAM,6FAAA,CAAA,WAAE,CAAC,SAAS,CAAC,UAAU;IAE7B,OAAO,CAAC,QAAQ,EAAE,UAAU;AAC9B", "debugId": null}}, {"offset": {"line": 178, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/activitiesdates/src/app/api/activities/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { readActivities, addActivity, updateActivity, deleteActivity } from '@/lib/storage';\nimport { Activity, ActivityCategory } from '@/types';\n\n// GET - Fetch all activities\nexport async function GET(request: NextRequest) {\n  try {\n    const activities = await readActivities();\n    \n    // Apply filters from query params\n    const { searchParams } = new URL(request.url);\n    const category = searchParams.get('category') as ActivityCategory;\n    const completed = searchParams.get('completed');\n    const search = searchParams.get('search');\n    \n    let filteredActivities = activities;\n    \n    if (category) {\n      filteredActivities = filteredActivities.filter(a => a.category === category);\n    }\n    \n    if (completed !== null) {\n      const isCompleted = completed === 'true';\n      filteredActivities = filteredActivities.filter(a => a.completed === isCompleted);\n    }\n    \n    if (search) {\n      const searchLower = search.toLowerCase();\n      filteredActivities = filteredActivities.filter(a => \n        a.title.toLowerCase().includes(searchLower) ||\n        a.description.toLowerCase().includes(searchLower) ||\n        a.tags.some(tag => tag.toLowerCase().includes(searchLower))\n      );\n    }\n    \n    // Sort by created date (newest first)\n    filteredActivities.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());\n    \n    return NextResponse.json({ activities: filteredActivities });\n  } catch (error) {\n    console.error('Error fetching activities:', error);\n    return NextResponse.json({ error: 'Failed to fetch activities' }, { status: 500 });\n  }\n}\n\n// POST - Create new activity\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    \n    // Validate required fields\n    if (!body.title || !body.description || body.price === undefined || !body.category) {\n      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });\n    }\n    \n    const activityData = {\n      title: body.title,\n      description: body.description,\n      price: parseFloat(body.price),\n      category: body.category,\n      plannedDate: body.plannedDate || undefined,\n      completedDate: undefined,\n      images: body.images || [],\n      completed: false,\n      rating: undefined,\n      notes: body.notes || '',\n      tags: body.tags || [],\n      location: body.location || undefined,\n      duration: body.duration ? parseInt(body.duration) : undefined,\n    };\n    \n    const newActivity = await addActivity(activityData);\n    return NextResponse.json({ activity: newActivity }, { status: 201 });\n  } catch (error) {\n    console.error('Error creating activity:', error);\n    return NextResponse.json({ error: 'Failed to create activity' }, { status: 500 });\n  }\n}\n\n// PUT - Update activity\nexport async function PUT(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const { id, ...updates } = body;\n    \n    if (!id) {\n      return NextResponse.json({ error: 'Activity ID is required' }, { status: 400 });\n    }\n    \n    const updatedActivity = await updateActivity(id, updates);\n    \n    if (!updatedActivity) {\n      return NextResponse.json({ error: 'Activity not found' }, { status: 404 });\n    }\n    \n    return NextResponse.json({ activity: updatedActivity });\n  } catch (error) {\n    console.error('Error updating activity:', error);\n    return NextResponse.json({ error: 'Failed to update activity' }, { status: 500 });\n  }\n}\n\n// DELETE - Delete activity\nexport async function DELETE(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const id = searchParams.get('id');\n    \n    if (!id) {\n      return NextResponse.json({ error: 'Activity ID is required' }, { status: 400 });\n    }\n    \n    const deleted = await deleteActivity(id);\n    \n    if (!deleted) {\n      return NextResponse.json({ error: 'Activity not found' }, { status: 404 });\n    }\n    \n    return NextResponse.json({ message: 'Activity deleted successfully' });\n  } catch (error) {\n    console.error('Error deleting activity:', error);\n    return NextResponse.json({ error: 'Failed to delete activity' }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAIO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,aAAa,MAAM,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD;QAEtC,kCAAkC;QAClC,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,MAAM,YAAY,aAAa,GAAG,CAAC;QACnC,MAAM,SAAS,aAAa,GAAG,CAAC;QAEhC,IAAI,qBAAqB;QAEzB,IAAI,UAAU;YACZ,qBAAqB,mBAAmB,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK;QACrE;QAEA,IAAI,cAAc,MAAM;YACtB,MAAM,cAAc,cAAc;YAClC,qBAAqB,mBAAmB,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,KAAK;QACtE;QAEA,IAAI,QAAQ;YACV,MAAM,cAAc,OAAO,WAAW;YACtC,qBAAqB,mBAAmB,MAAM,CAAC,CAAA,IAC7C,EAAE,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,gBAC/B,EAAE,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACrC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,WAAW,GAAG,QAAQ,CAAC;QAElD;QAEA,sCAAsC;QACtC,mBAAmB,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;QAEjG,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,YAAY;QAAmB;IAC5D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAA6B,GAAG;YAAE,QAAQ;QAAI;IAClF;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAE/B,2BAA2B;QAC3B,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,WAAW,IAAI,KAAK,KAAK,KAAK,aAAa,CAAC,KAAK,QAAQ,EAAE;YAClF,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAA0B,GAAG;gBAAE,QAAQ;YAAI;QAC/E;QAEA,MAAM,eAAe;YACnB,OAAO,KAAK,KAAK;YACjB,aAAa,KAAK,WAAW;YAC7B,OAAO,WAAW,KAAK,KAAK;YAC5B,UAAU,KAAK,QAAQ;YACvB,aAAa,KAAK,WAAW,IAAI;YACjC,eAAe;YACf,QAAQ,KAAK,MAAM,IAAI,EAAE;YACzB,WAAW;YACX,QAAQ;YACR,OAAO,KAAK,KAAK,IAAI;YACrB,MAAM,KAAK,IAAI,IAAI,EAAE;YACrB,UAAU,KAAK,QAAQ,IAAI;YAC3B,UAAU,KAAK,QAAQ,GAAG,SAAS,KAAK,QAAQ,IAAI;QACtD;QAEA,MAAM,cAAc,MAAM,CAAA,GAAA,uHAAA,CAAA,cAAW,AAAD,EAAE;QACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,UAAU;QAAY,GAAG;YAAE,QAAQ;QAAI;IACpE,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAA4B,GAAG;YAAE,QAAQ;QAAI;IACjF;AACF;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,EAAE,EAAE,GAAG,SAAS,GAAG;QAE3B,IAAI,CAAC,IAAI;YACP,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAA0B,GAAG;gBAAE,QAAQ;YAAI;QAC/E;QAEA,MAAM,kBAAkB,MAAM,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE,IAAI;QAEjD,IAAI,CAAC,iBAAiB;YACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAqB,GAAG;gBAAE,QAAQ;YAAI;QAC1E;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,UAAU;QAAgB;IACvD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAA4B,GAAG;YAAE,QAAQ;QAAI;IACjF;AACF;AAGO,eAAe,OAAO,OAAoB;IAC/C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,KAAK,aAAa,GAAG,CAAC;QAE5B,IAAI,CAAC,IAAI;YACP,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAA0B,GAAG;gBAAE,QAAQ;YAAI;QAC/E;QAEA,MAAM,UAAU,MAAM,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE;QAErC,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAqB,GAAG;gBAAE,QAAQ;YAAI;QAC1E;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;QAAgC;IACtE,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAA4B,GAAG;YAAE,QAAQ;QAAI;IACjF;AACF", "debugId": null}}]}