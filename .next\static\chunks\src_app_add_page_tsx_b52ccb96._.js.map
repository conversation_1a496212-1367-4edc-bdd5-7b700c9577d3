{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/activitiesdates/src/app/add/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useForm } from 'react-hook-form';\nimport { ActivityFormData, ActivityCategory } from '@/types';\nimport { ArrowLeft, Save, Heart } from 'lucide-react';\nimport Link from 'next/link';\n\nexport default function AddActivity() {\n  const router = useRouter();\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [submitError, setSubmitError] = useState('');\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors },\n    reset,\n  } = useForm<ActivityFormData>();\n\n  const categories: { value: ActivityCategory; label: string }[] = [\n    { value: 'dining', label: 'Dining' },\n    { value: 'entertainment', label: 'Entertainment' },\n    { value: 'outdoor', label: 'Outdoor' },\n    { value: 'cultural', label: 'Cultural' },\n    { value: 'romantic', label: 'Romantic' },\n    { value: 'adventure', label: 'Adventure' },\n    { value: 'relaxation', label: 'Relaxation' },\n    { value: 'travel', label: 'Travel' },\n    { value: 'sports', label: 'Sports' },\n    { value: 'other', label: 'Other' },\n  ];\n\n  const onSubmit = async (data: ActivityFormData) => {\n    setIsSubmitting(true);\n    setSubmitError('');\n\n    try {\n      // Process tags\n      const tags = data.tags\n        ? data.tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0)\n        : [];\n\n      const activityData = {\n        ...data,\n        tags,\n        price: parseFloat(data.price.toString()),\n        duration: data.duration ? parseInt(data.duration.toString()) : undefined,\n      };\n\n      const response = await fetch('/api/activities', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(activityData),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.error || 'Failed to create activity');\n      }\n\n      // Success! Redirect to home page\n      router.push('/');\n    } catch (error) {\n      console.error('Error creating activity:', error);\n      setSubmitError(error instanceof Error ? error.message : 'Failed to create activity');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <div className=\"max-w-2xl mx-auto\">\n      {/* Header */}\n      <div className=\"flex items-center space-x-4 mb-8\">\n        <Link\n          href=\"/\"\n          className=\"p-2 rounded-full bg-white/80 backdrop-blur-sm border border-white/20 text-gray-600 hover:text-purple-600 transition-colors\"\n        >\n          <ArrowLeft className=\"h-5 w-5\" />\n        </Link>\n        <div>\n          <h1 className=\"text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent\">\n            Add New Activity\n          </h1>\n          <p className=\"text-gray-600\">Plan your next perfect date or activity</p>\n        </div>\n      </div>\n\n      {/* Form */}\n      <div className=\"bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-8\">\n        <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\n          {/* Title */}\n          <div>\n            <label htmlFor=\"title\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Activity Title *\n            </label>\n            <input\n              type=\"text\"\n              id=\"title\"\n              {...register('title', { required: 'Title is required' })}\n              className=\"w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200\"\n              placeholder=\"e.g., Romantic dinner at Italian restaurant\"\n            />\n            {errors.title && (\n              <p className=\"mt-1 text-sm text-red-600\">{errors.title.message}</p>\n            )}\n          </div>\n\n          {/* Description */}\n          <div>\n            <label htmlFor=\"description\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Description *\n            </label>\n            <textarea\n              id=\"description\"\n              rows={4}\n              {...register('description', { required: 'Description is required' })}\n              className=\"w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 resize-none\"\n              placeholder=\"Describe the activity, what makes it special, any details to remember...\"\n            />\n            {errors.description && (\n              <p className=\"mt-1 text-sm text-red-600\">{errors.description.message}</p>\n            )}\n          </div>\n\n          {/* Price and Category Row */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div>\n              <label htmlFor=\"price\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Estimated Price *\n              </label>\n              <div className=\"relative\">\n                <span className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500\">$</span>\n                <input\n                  type=\"number\"\n                  id=\"price\"\n                  step=\"0.01\"\n                  min=\"0\"\n                  {...register('price', { \n                    required: 'Price is required',\n                    min: { value: 0, message: 'Price must be positive' }\n                  })}\n                  className=\"w-full pl-8 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200\"\n                  placeholder=\"0.00\"\n                />\n              </div>\n              {errors.price && (\n                <p className=\"mt-1 text-sm text-red-600\">{errors.price.message}</p>\n              )}\n            </div>\n\n            <div>\n              <label htmlFor=\"category\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Category *\n              </label>\n              <select\n                id=\"category\"\n                {...register('category', { required: 'Category is required' })}\n                className=\"w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200\"\n              >\n                <option value=\"\">Select a category</option>\n                {categories.map((category) => (\n                  <option key={category.value} value={category.value}>\n                    {category.label}\n                  </option>\n                ))}\n              </select>\n              {errors.category && (\n                <p className=\"mt-1 text-sm text-red-600\">{errors.category.message}</p>\n              )}\n            </div>\n          </div>\n\n          {/* Date and Location Row */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div>\n              <label htmlFor=\"plannedDate\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Planned Date\n              </label>\n              <input\n                type=\"date\"\n                id=\"plannedDate\"\n                {...register('plannedDate')}\n                className=\"w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200\"\n              />\n            </div>\n\n            <div>\n              <label htmlFor=\"location\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Location\n              </label>\n              <input\n                type=\"text\"\n                id=\"location\"\n                {...register('location')}\n                className=\"w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200\"\n                placeholder=\"e.g., Downtown, Central Park\"\n              />\n            </div>\n          </div>\n\n          {/* Duration and Tags Row */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div>\n              <label htmlFor=\"duration\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Duration (minutes)\n              </label>\n              <input\n                type=\"number\"\n                id=\"duration\"\n                min=\"1\"\n                {...register('duration')}\n                className=\"w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200\"\n                placeholder=\"e.g., 120\"\n              />\n            </div>\n\n            <div>\n              <label htmlFor=\"tags\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Tags\n              </label>\n              <input\n                type=\"text\"\n                id=\"tags\"\n                {...register('tags')}\n                className=\"w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200\"\n                placeholder=\"e.g., cozy, special occasion, weekend\"\n              />\n              <p className=\"mt-1 text-xs text-gray-500\">Separate tags with commas</p>\n            </div>\n          </div>\n\n          {/* Notes */}\n          <div>\n            <label htmlFor=\"notes\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Additional Notes\n            </label>\n            <textarea\n              id=\"notes\"\n              rows={3}\n              {...register('notes')}\n              className=\"w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 resize-none\"\n              placeholder=\"Any special notes, reminders, or ideas...\"\n            />\n          </div>\n\n          {/* Error Message */}\n          {submitError && (\n            <div className=\"p-4 bg-red-50 border border-red-200 rounded-xl\">\n              <p className=\"text-red-600 text-sm\">{submitError}</p>\n            </div>\n          )}\n\n          {/* Submit Button */}\n          <div className=\"flex items-center justify-end space-x-4 pt-6\">\n            <Link\n              href=\"/\"\n              className=\"px-6 py-3 border border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 transition-colors\"\n            >\n              Cancel\n            </Link>\n            <button\n              type=\"submit\"\n              disabled={isSubmitting}\n              className=\"flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-xl hover:from-purple-600 hover:to-pink-600 transition-all duration-200 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {isSubmitting ? (\n                <>\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent\" />\n                  <span>Creating...</span>\n                </>\n              ) : (\n                <>\n                  <Save className=\"h-4 w-4\" />\n                  <span>Create Activity</span>\n                </>\n              )}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AAAA;AACA;;;AAPA;;;;;;AASe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACrB,KAAK,EACN,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD;IAEV,MAAM,aAA2D;QAC/D;YAAE,OAAO;YAAU,OAAO;QAAS;QACnC;YAAE,OAAO;YAAiB,OAAO;QAAgB;QACjD;YAAE,OAAO;YAAW,OAAO;QAAU;QACrC;YAAE,OAAO;YAAY,OAAO;QAAW;QACvC;YAAE,OAAO;YAAY,OAAO;QAAW;QACvC;YAAE,OAAO;YAAa,OAAO;QAAY;QACzC;YAAE,OAAO;YAAc,OAAO;QAAa;QAC3C;YAAE,OAAO;YAAU,OAAO;QAAS;QACnC;YAAE,OAAO;YAAU,OAAO;QAAS;QACnC;YAAE,OAAO;YAAS,OAAO;QAAQ;KAClC;IAED,MAAM,WAAW,OAAO;QACtB,gBAAgB;QAChB,eAAe;QAEf,IAAI;YACF,eAAe;YACf,MAAM,OAAO,KAAK,IAAI,GAClB,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI,IAAI,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,GAAG,KACvE,EAAE;YAEN,MAAM,eAAe;gBACnB,GAAG,IAAI;gBACP;gBACA,OAAO,WAAW,KAAK,KAAK,CAAC,QAAQ;gBACrC,UAAU,KAAK,QAAQ,GAAG,SAAS,KAAK,QAAQ,CAAC,QAAQ,MAAM;YACjE;YAEA,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;YACrC;YAEA,iCAAiC;YACjC,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC1D,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;kCAEV,cAAA,6LAAC,mNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;;;;;;kCAEvB,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAgG;;;;;;0CAG9G,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;0BAKjC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAK,UAAU,aAAa;oBAAW,WAAU;;sCAEhD,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAQ,WAAU;8CAA+C;;;;;;8CAGhF,6LAAC;oCACC,MAAK;oCACL,IAAG;oCACF,GAAG,SAAS,SAAS;wCAAE,UAAU;oCAAoB,EAAE;oCACxD,WAAU;oCACV,aAAY;;;;;;gCAEb,OAAO,KAAK,kBACX,6LAAC;oCAAE,WAAU;8CAA6B,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;sCAKlE,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAc,WAAU;8CAA+C;;;;;;8CAGtF,6LAAC;oCACC,IAAG;oCACH,MAAM;oCACL,GAAG,SAAS,eAAe;wCAAE,UAAU;oCAA0B,EAAE;oCACpE,WAAU;oCACV,aAAY;;;;;;gCAEb,OAAO,WAAW,kBACjB,6LAAC;oCAAE,WAAU;8CAA6B,OAAO,WAAW,CAAC,OAAO;;;;;;;;;;;;sCAKxE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAA+C;;;;;;sDAGhF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAmE;;;;;;8DACnF,6LAAC;oDACC,MAAK;oDACL,IAAG;oDACH,MAAK;oDACL,KAAI;oDACH,GAAG,SAAS,SAAS;wDACpB,UAAU;wDACV,KAAK;4DAAE,OAAO;4DAAG,SAAS;wDAAyB;oDACrD,EAAE;oDACF,WAAU;oDACV,aAAY;;;;;;;;;;;;wCAGf,OAAO,KAAK,kBACX,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;8CAIlE,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAA+C;;;;;;sDAGnF,6LAAC;4CACC,IAAG;4CACF,GAAG,SAAS,YAAY;gDAAE,UAAU;4CAAuB,EAAE;4CAC9D,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAG;;;;;;gDAChB,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC;wDAA4B,OAAO,SAAS,KAAK;kEAC/C,SAAS,KAAK;uDADJ,SAAS,KAAK;;;;;;;;;;;wCAK9B,OAAO,QAAQ,kBACd,6LAAC;4CAAE,WAAU;sDAA6B,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;;;;;;;sCAMvE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAc,WAAU;sDAA+C;;;;;;sDAGtF,6LAAC;4CACC,MAAK;4CACL,IAAG;4CACF,GAAG,SAAS,cAAc;4CAC3B,WAAU;;;;;;;;;;;;8CAId,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAA+C;;;;;;sDAGnF,6LAAC;4CACC,MAAK;4CACL,IAAG;4CACF,GAAG,SAAS,WAAW;4CACxB,WAAU;4CACV,aAAY;;;;;;;;;;;;;;;;;;sCAMlB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAA+C;;;;;;sDAGnF,6LAAC;4CACC,MAAK;4CACL,IAAG;4CACH,KAAI;4CACH,GAAG,SAAS,WAAW;4CACxB,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAIhB,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAO,WAAU;sDAA+C;;;;;;sDAG/E,6LAAC;4CACC,MAAK;4CACL,IAAG;4CACF,GAAG,SAAS,OAAO;4CACpB,WAAU;4CACV,aAAY;;;;;;sDAEd,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;;;;;;;sCAK9C,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAQ,WAAU;8CAA+C;;;;;;8CAGhF,6LAAC;oCACC,IAAG;oCACH,MAAM;oCACL,GAAG,SAAS,QAAQ;oCACrB,WAAU;oCACV,aAAY;;;;;;;;;;;;wBAKf,6BACC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;sCAKzC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,MAAK;oCACL,UAAU;oCACV,WAAU;8CAET,6BACC;;0DACE,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;0DAAK;;;;;;;qEAGR;;0DACE,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxB;GAtRwB;;QACP,qIAAA,CAAA,YAAS;QASpB,iKAAA,CAAA,UAAO;;;KAVW", "debugId": null}}]}