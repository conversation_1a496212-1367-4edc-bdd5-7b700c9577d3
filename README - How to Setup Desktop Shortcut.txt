💕 DATE PLANNER - <PERSON>S<PERSON>TO<PERSON> SHORTCUT SETUP 💕
================================================

🚀 QUICK START:
1. Double-click "start-date-planner.bat" to run the app
2. The app will start and open in your browser automatically
3. Keep the command window open while using the app

📌 CREATE DESKTOP SHORTCUT:

METHOD 1 - Automatic (Easiest):
1. Double-click "create-shortcut.bat"
2. A shortcut will appear on your desktop!

METHOD 2 - PowerShell (Alternative):
1. Right-click "create-shortcut.ps1"
2. Select "Run with PowerShell"
3. A shortcut will appear on your desktop!

METHOD 3 - Manual:
1. Right-click "start-date-planner.bat"
2. Select "Create shortcut"
3. Drag the shortcut to your desktop
4. Right-click the shortcut → Properties
5. Change the name to "💕 Date Planner" (or whatever you like)

🎨 CUSTOMIZE THE ICON:
1. Right-click your desktop shortcut
2. Select "Properties"
3. Click "Change Icon..."
4. Choose from these options:
   - Browse system icons (shell32.dll has many options)
   - Download a custom .ico file online
   - Use icon from index 23 in shell32.dll (heart-like)
   - Use icon from index 137 in shell32.dll (calendar)

🌐 POPULAR ICON WEBSITES:
- icons8.com (search "heart" or "love")
- flaticon.com
- iconarchive.com
- Convert PNG to ICO at: convertio.co

💡 PRO TIPS:
- Pin the shortcut to your taskbar for quick access
- Add it to your Start Menu
- The app saves all data locally in the "data" folder
- You can backup your activities by copying the "data" folder

🔧 TROUBLESHOOTING:
- If the app doesn't start, make sure Node.js is installed
- If browser doesn't open, manually go to http://localhost:3000
- Keep the command window open while using the app
- Close the command window to stop the app

📁 FILES INCLUDED:
- start-date-planner.bat (main launcher)
- create-shortcut.ps1 (automatic shortcut creator)
- data/activities.json (your activities data)
- data/uploads/ (folder for images)

Enjoy planning your perfect dates! 💕
